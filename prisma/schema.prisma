// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  role          UserRole  @default(USER)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  password      String   // <-- Added password field

  // Relations
  accounts Account[]
  sessions Session[]
  orders   Order[]
  reviews  Review[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Product Management
model Product {
  id           String   @id @default(cuid())
  name         String
  description  String   @db.Text
  price        Decimal  @db.Decimal(10, 2)
  originalPrice Decimal? @db.Decimal(10, 2)
  category     ProductCategory
  slug         String   @unique
  images       String[]
  colors       Json?
  sizes        Json?
  stock        Int      @default(0)
  badge        String?
  featured     Boolean  @default(false)
  tags         String[]
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  orderItems OrderItem[]
  reviews    Review[]

  @@map("products")
}

model Review {
  id        String   @id @default(cuid())
  rating    Int
  comment   String?  @db.Text
  userId    String
  productId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("reviews")
}

// Order Management
model Order {
  id              String      @id @default(cuid())
  orderNumber     String      @unique
  status          OrderStatus @default(PENDING)
  total           Decimal     @db.Decimal(10, 2)
  subtotal        Decimal     @db.Decimal(10, 2)
  shipping        Decimal     @db.Decimal(10, 2)
  tax             Decimal     @db.Decimal(10, 2)
  paymentIntentId String?
  userId          String?
  customerEmail   String
  shippingAddress Json
  billingAddress  Json?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  user       User?       @relation(fields: [userId], references: [id])
  orderItems OrderItem[]

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  quantity  Int
  price     Decimal @db.Decimal(10, 2)
  orderId   String
  productId String
  options   Json? // color, size, etc.

  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Content Management
model BlogPost {
  id            String   @id @default(cuid())
  title         String
  slug          String   @unique
  excerpt       String   @db.Text
  content       String   @db.Text
  author        String
  featuredImage String
  tags          String[]
  category      String
  featured      Boolean  @default(false)
  published     Boolean  @default(false)
  publishedAt   DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("blog_posts")
}

model Track {
  id          String   @id @default(cuid())
  title       String
  artist      String
  album       String
  duration    Int // in seconds
  url         String?
  artwork     String
  genre       String[]
  featured    Boolean  @default(false)
  releaseDate DateTime
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("tracks")
}

model Show {
  id       String   @id @default(cuid())
  title    String
  venue    String
  city     String
  state    String?
  country  String
  date     DateTime
  time     String
  price    Decimal? @db.Decimal(10, 2)
  soldOut  Boolean  @default(false)
  featured Boolean  @default(false)
  ticketUrl String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("shows")
}

// Contact & Newsletter
model ContactSubmission {
  id        String   @id @default(cuid())
  name      String
  email     String
  subject   String
  message   String   @db.Text
  status    String   @default("new") // new, read, replied
  createdAt DateTime @default(now())

  @@map("contact_submissions")
}

model NewsletterSubscriber {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String?
  active      Boolean  @default(true)
  subscribedAt DateTime @default(now())
  unsubscribedAt DateTime?

  @@map("newsletter_subscribers")
}

// Enums
enum UserRole {
  USER
  ADMIN
}

enum ProductCategory {
  clothing
  music
  accessories
  bundles
}

enum OrderStatus {
  PENDING
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}
