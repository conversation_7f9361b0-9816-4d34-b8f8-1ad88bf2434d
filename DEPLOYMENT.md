# Heroku Deployment Guide

This guide will walk you through deploying your Worship Leader Christian Music E-commerce platform to Heroku.

## Prerequisites

1. **Heroku Account**: Sign up at [heroku.com](https://heroku.com)
2. **Heroku CLI**: Install from [devcenter.heroku.com/articles/heroku-cli](https://devcenter.heroku.com/articles/heroku-cli)
3. **Git**: Ensure your project is in a Git repository

## Quick Deploy

### Option 1: One-Click Deploy
[![Deploy](https://www.herokucdn.com/deploy/button.svg)](https://heroku.com/deploy)

### Option 2: Manual Deployment

## Step 1: Create Heroku App

```bash
# Login to Heroku
heroku login

# Create a new app (replace 'your-app-name' with your desired name)
heroku create your-worship-leader-app

# Add PostgreSQL database
heroku addons:create heroku-postgresql:essential-0

# Add Redis for caching (optional)
heroku addons:create heroku-redis:mini
```

## Step 2: Configure Environment Variables

Set all required environment variables:

```bash
# Database (automatically set by Heroku PostgreSQL addon)
# DATABASE_URL is automatically configured

# NextAuth.js
heroku config:set NEXTAUTH_SECRET=$(openssl rand -base64 32)
heroku config:set NEXTAUTH_URL=https://your-app-name.herokuapp.com

# Stripe (get these from your Stripe dashboard)
heroku config:set NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_key
heroku config:set STRIPE_SECRET_KEY=sk_live_your_key
heroku config:set STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email (using Gmail as example)
heroku config:set EMAIL_SERVER_HOST=smtp.gmail.com
heroku config:set EMAIL_SERVER_PORT=587
heroku config:set EMAIL_SERVER_USER=<EMAIL>
heroku config:set EMAIL_SERVER_PASSWORD=your-app-password
heroku config:set EMAIL_FROM=<EMAIL>

# Admin
heroku config:set ADMIN_EMAIL=<EMAIL>

# Site URL
heroku config:set SITE_URL=https://your-app-name.herokuapp.com

# Google OAuth (optional)
heroku config:set GOOGLE_CLIENT_ID=your-google-client-id
heroku config:set GOOGLE_CLIENT_SECRET=your-google-client-secret

# Analytics (optional)
heroku config:set NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

## Step 3: Deploy

```bash
# Add Heroku remote (if not already added)
heroku git:remote -a your-app-name

# Deploy to Heroku
git push heroku main

# Run database migrations
heroku run npm run db:migrate

# Seed the database with initial data
heroku run npm run db:seed
```

## Step 4: Configure Stripe Webhooks

1. Go to your [Stripe Dashboard](https://dashboard.stripe.com/webhooks)
2. Click "Add endpoint"
3. Set the endpoint URL to: `https://your-app-name.herokuapp.com/api/webhooks/stripe`
4. Select these events:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
5. Copy the webhook secret and update your Heroku config:
   ```bash
   heroku config:set STRIPE_WEBHOOK_SECRET=whsec_your_new_secret
   ```

## Step 5: Set Up Custom Domain (Optional)

```bash
# Add your custom domain
heroku domains:add your-domain.com
heroku domains:add www.your-domain.com

# Get DNS target
heroku domains

# Update your DNS settings to point to the Heroku DNS target
```

## Step 6: Configure SSL (Automatic with Custom Domains)

```bash
# Enable Automated Certificate Management
heroku certs:auto:enable
```

## Environment Variables Reference

| Variable | Description | Required |
|----------|-------------|----------|
| `DATABASE_URL` | PostgreSQL connection string | ✅ |
| `NEXTAUTH_SECRET` | Secret for JWT encryption | ✅ |
| `NEXTAUTH_URL` | Canonical URL of your site | ✅ |
| `STRIPE_SECRET_KEY` | Stripe secret key | ✅ |
| `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` | Stripe publishable key | ✅ |
| `STRIPE_WEBHOOK_SECRET` | Stripe webhook secret | ✅ |
| `EMAIL_SERVER_HOST` | SMTP server host | ✅ |
| `EMAIL_SERVER_USER` | SMTP username | ✅ |
| `EMAIL_SERVER_PASSWORD` | SMTP password | ✅ |
| `EMAIL_FROM` | From email address | ✅ |
| `ADMIN_EMAIL` | Admin email for notifications | ✅ |
| `SITE_URL` | Your site's URL | ✅ |
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | ❌ |
| `GOOGLE_CLIENT_SECRET` | Google OAuth secret | ❌ |
| `NEXT_PUBLIC_GA_MEASUREMENT_ID` | Google Analytics ID | ❌ |

## Post-Deployment Checklist

- [ ] Test the website functionality
- [ ] Test user registration and login
- [ ] Test product purchases with Stripe test cards
- [ ] Test contact form submissions
- [ ] Test newsletter signup
- [ ] Verify email notifications are working
- [ ] Check admin dashboard access
- [ ] Test mobile responsiveness
- [ ] Verify SEO meta tags
- [ ] Set up monitoring and alerts

## Monitoring and Maintenance

### View Logs
```bash
heroku logs --tail
```

### Scale Dynos
```bash
# Scale web dynos
heroku ps:scale web=1

# For higher traffic, scale up
heroku ps:scale web=2
```

### Database Management
```bash
# Access database
heroku pg:psql

# Create backup
heroku pg:backups:capture

# View backups
heroku pg:backups
```

### Performance Monitoring
- Use Heroku Metrics dashboard
- Set up New Relic or other APM tools
- Monitor database performance

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check `heroku logs` for build errors
   - Ensure all dependencies are in `package.json`
   - Verify Node.js version compatibility

2. **Database Connection Issues**
   - Verify `DATABASE_URL` is set
   - Check if migrations ran successfully
   - Ensure Prisma client is generated

3. **Environment Variable Issues**
   - Use `heroku config` to verify all variables are set
   - Check for typos in variable names
   - Ensure sensitive values are properly escaped

4. **Payment Issues**
   - Verify Stripe keys are correct
   - Check webhook endpoint configuration
   - Test with Stripe test cards

### Support

For additional help:
- Check Heroku documentation
- Review application logs
- Contact support if needed

## Security Considerations

- Use strong, unique passwords
- Enable two-factor authentication on all accounts
- Regularly update dependencies
- Monitor for security vulnerabilities
- Use HTTPS everywhere
- Implement proper CORS policies
- Validate all user inputs
- Use environment variables for secrets
