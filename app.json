{"name": "Worship Leader - Christian Music Store", "description": "A modern Christian music e-commerce platform built with Next.js", "repository": "https://github.com/your-username/worship-leader-store", "logo": "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200", "keywords": ["nextjs", "react", "ecommerce", "christian-music", "worship", "typescript"], "image": "hero<PERSON>/nodejs", "stack": "heroku-22", "buildpacks": [{"url": "hero<PERSON>/nodejs"}], "formation": {"web": {"quantity": 1, "size": "basic"}}, "addons": [{"plan": "heroku-postgresql:essential-0"}, {"plan": "heroku-redis:mini"}], "env": {"NODE_ENV": {"description": "Node environment", "value": "production"}, "NEXTAUTH_SECRET": {"description": "NextAuth.js secret for JWT encryption", "generator": "secret"}, "NEXTAUTH_URL": {"description": "Canonical URL of your site", "value": "https://your-app-name.herokuapp.com"}, "STRIPE_PUBLISHABLE_KEY": {"description": "Stripe publishable key for payments"}, "STRIPE_SECRET_KEY": {"description": "Stripe secret key for payments"}, "STRIPE_WEBHOOK_SECRET": {"description": "Stripe webhook secret for payment verification"}, "EMAIL_SERVER_HOST": {"description": "SMTP server host for sending emails"}, "EMAIL_SERVER_PORT": {"description": "SMTP server port", "value": "587"}, "EMAIL_SERVER_USER": {"description": "SMTP server username"}, "EMAIL_SERVER_PASSWORD": {"description": "SMTP server password"}, "EMAIL_FROM": {"description": "Email address to send emails from"}, "GOOGLE_CLIENT_ID": {"description": "Google OAuth client ID"}, "GOOGLE_CLIENT_SECRET": {"description": "Google OAuth client secret"}, "ADMIN_EMAIL": {"description": "Admin email address for access control"}}, "scripts": {"postdeploy": "npm run db:migrate && npm run db:seed"}}