{"name": "musiceco-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:migrate": "prisma migrate deploy", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "postinstall": "prisma generate", "heroku-postbuild": "npm run build"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.17.0", "@stripe/stripe-js": "^4.1.0", "@types/node": "^20.14.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "embla-carousel-react": "^8.1.6", "framer-motion": "^11.3.8", "lucide-react": "^0.408.0", "next": "^14.2.30", "next-auth": "^4.24.7", "next-sitemap": "^4.2.3", "nodemailer": "^6.9.14", "postcss": "^8.4.39", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.1", "react-intersection-observer": "^9.13.0", "sharp": "^0.33.4", "stripe": "^16.5.0", "tailwind-merge": "^2.4.0", "tailwindcss": "^3.4.6", "tsx": "^4.16.2", "typescript": "^5.5.4", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.6", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.15", "@testing-library/react": "^16.0.0", "@types/jest": "^29.5.12", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.3.3", "prisma": "^5.17.0", "prettier-plugin-tailwindcss": "^0.6.5"}, "engines": {"node": ">=18.0.0"}}