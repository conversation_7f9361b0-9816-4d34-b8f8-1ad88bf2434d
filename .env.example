# Database
DATABASE_URL="postgresql://username:password@localhost:5432/worship_leader_db"

# NextAuth.js
NEXTAUTH_SECRET="your-nextauth-secret-here"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth (optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Stripe Payment Processing
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"

# Email Configuration (SMTP)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT="587"
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# Admin Configuration
ADMIN_EMAIL="<EMAIL>"

# Site Configuration
SITE_URL="https://your-app-name.herokuapp.com"
NODE_ENV="production"

# Analytics (optional)
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-XXXXXXXXXX"

# Redis (for caching - optional)
REDIS_URL="redis://localhost:6379"
