#!/bin/bash

# Worship Leader Platform Setup Script
# This script will help you set up the complete Christian music e-commerce platform

echo "🎵 Setting up Worship Leader Platform..."
echo "========================================="

# Check if git is installed
if ! command -v git &> /dev/null; then
    echo "❌ Git is not installed. Please install Git first."
    exit 1
fi

# Check if node is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Clone the repository
echo "📥 Cloning repository..."
git clone https://github.com/joelgriiyo/musiceco.git
cd musiceco

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Install additional dependencies for the full platform
echo "📦 Installing additional dependencies..."
npm install @next-auth/prisma-adapter @prisma/client @stripe/stripe-js bcryptjs next-auth next-sitemap nodemailer stripe tsx
npm install -D @types/bcryptjs @types/nodemailer prisma

# Create environment file
echo "⚙️ Creating environment file..."
cp .env.example .env.local 2>/dev/null || echo "# Copy from .env.example and configure your values" > .env.local

# Initialize Prisma
echo "🗄️ Setting up database..."
npx prisma generate

echo "✅ Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Edit .env.local with your configuration values"
echo "2. Set up your PostgreSQL database"
echo "3. Run: npm run db:migrate"
echo "4. Run: npm run db:seed"
echo "5. Run: npm run dev"
echo ""
echo "🚀 For Heroku deployment, see DEPLOYMENT.md"
echo ""
echo "🎵 Happy coding! May this platform bless many through music and ministry."
