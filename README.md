# 🎵 Worship Leader - Christian Music E-commerce Platform

A cutting-edge, fully responsive Christian music artist website and e-commerce platform built with Next.js 14, TypeScript, and modern web technologies. Features a stunning dark theme with yellow, red, and white accents, glass morphism effects, and smooth animations perfect for a worship ministry.

![Worship Leader Platform](https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=600)

## ✨ Features

### 🎨 Modern Design
- **Dark Theme**: Sleek black background with vibrant yellow, red, and white accents
- **Glass Morphism**: Transparent cards and components with backdrop blur effects
- **Gradient Elements**: Beautiful gradients throughout the interface
- **Responsive Design**: Optimized for all devices and screen sizes
- **Smooth Animations**: Framer Motion powered animations and transitions

### 🛍️ E-commerce Functionality
- **Product Catalog**: Browse Christian clothing, worship CDs, accessories, and ministry bundles
- **Shopping Cart**: Persistent cart with Zustand state management
- **Checkout System**: Secure checkout flow with form validation
- **Product Filtering**: Filter by category, search, and sort options
- **Wishlist**: Save favorite items for later

### 🎵 Music Features
- **Music Player**: Built-in audio player with worship playlist support
- **Track Management**: Browse and play worship music collection
- **Streaming Integration**: Links to Spotify, Apple Music, SoundCloud
- **Album Displays**: Beautiful album artwork and worship song listings

### 📱 Core Pages
- **Homepage**: Hero section with video background, featured worship songs, upcoming ministry events
- **Shop**: Complete e-commerce experience with Christian products
- **Music**: Worship music collection with integrated player
- **Blog**: Ministry content with devotionals and testimonies
- **About**: Ministry story and timeline
- **Contact**: Contact form with ministry booking and prayer requests

### 🚀 Technical Features
- **Next.js 14**: Latest version with App Router and Turbo
- **TypeScript**: Full type safety throughout the application
- **Tailwind CSS**: Utility-first CSS framework with custom design system
- **Framer Motion**: Smooth animations and page transitions
- **Zustand**: Lightweight state management for cart and music player
- **React Hook Form**: Form handling with validation
- **Responsive Images**: Optimized images with Next.js Image component

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with TypeScript
- **Styling**: Tailwind CSS with custom design system
- **Animations**: Framer Motion
- **State Management**: Zustand
- **Forms**: React Hook Form + Zod validation
- **Icons**: Lucide React
- **Image Optimization**: Next.js Image component
- **Development**: ESLint, Prettier, TypeScript

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd musiceco-nextjs
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Available Scripts

```bash
npm run dev          # Start development server with Turbo
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript type checking
npm test             # Run tests
npm run test:watch   # Run tests in watch mode
```

## 📁 Project Structure

```
src/
├── app/                 # Next.js App Router pages
│   ├── globals.css     # Global styles
│   ├── layout.tsx      # Root layout
│   ├── page.tsx        # Homepage
│   ├── shop/           # Shop pages
│   ├── music/          # Music pages
│   ├── blog/           # Blog pages
│   ├── about/          # About page
│   ├── contact/        # Contact page
│   └── checkout/       # Checkout page
├── components/         # Reusable components
│   ├── layout/         # Layout components
│   ├── home/           # Homepage components
│   ├── shop/           # E-commerce components
│   ├── music/          # Music player components
│   ├── blog/           # Blog components
│   ├── cart/           # Shopping cart components
│   └── contact/        # Contact form components
├── lib/                # Utility functions and constants
│   ├── utils.ts        # Helper functions
│   ├── constants.ts    # App constants
│   └── mock-data.ts    # Mock data for development
├── store/              # Zustand stores
│   ├── cart.ts         # Shopping cart state
│   └── music.ts        # Music player state
├── types/              # TypeScript type definitions
│   └── index.ts        # All type definitions
└── styles/             # Additional styles
```

## 🎨 Design System

### Color Palette
- **Primary Black**: `#0a0a0a`
- **Primary White**: `#ffffff`
- **Accent Yellow**: `#ffd700`
- **Accent Red**: `#ff3e41`
- **Dark Gray**: `#121212`
- **Light Gray**: `#333333`

### Typography
- **Headings**: Bebas Neue (bold, display font)
- **Body**: Montserrat (clean, readable)

### Components
- **Glass Cards**: Transparent backgrounds with backdrop blur
- **Gradient Buttons**: Yellow to red gradients
- **Smooth Animations**: Consistent timing and easing

## 🔧 Customization

### Adding New Products
Edit `src/lib/mock-data.ts` to add new products:

```typescript
export const mockProducts: Product[] = [
  {
    id: 'unique-id',
    name: 'Product Name',
    price: 29.99,
    category: 'clothing',
    // ... other properties
  }
]
```

### Modifying Colors
Update `tailwind.config.ts` to change the color scheme:

```typescript
colors: {
  savage: {
    black: '#your-color',
    yellow: '#your-color',
    // ... other colors
  }
}
```

### Adding New Pages
Create new pages in the `src/app/` directory following Next.js App Router conventions.

## 🌟 Key Features Explained

### Shopping Cart
- Persistent storage using Zustand
- Real-time updates across components
- Automatic tax and shipping calculation
- Smooth sidebar animations

### Music Player
- Global state management
- Playlist support with shuffle and repeat
- Progress tracking and volume control
- Integration with track listings

### Responsive Design
- Mobile-first approach
- Breakpoint-specific layouts
- Touch-friendly interactions
- Optimized performance on all devices

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically with each push

### Other Platforms
The app can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- DigitalOcean App Platform

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Design inspiration from modern music platforms
- Icons by Lucide React
- Images from Unsplash
- Fonts from Google Fonts

---

**Built with ❤️ for the music community**
