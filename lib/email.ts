import nodemailer from 'nodemailer'

const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_SERVER_HOST,
  port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.EMAIL_SERVER_USER,
    pass: process.env.EMAIL_SERVER_PASSWORD,
  },
})

export interface EmailOptions {
  to: string
  subject: string
  html: string
  text?: string
}

export async function sendEmail({ to, subject, html, text }: EmailOptions) {
  try {
    const info = await transporter.sendMail({
      from: process.env.EMAIL_FROM,
      to,
      subject,
      html,
      text,
    })

    console.log('Email sent:', info.messageId)
    return { success: true, messageId: info.messageId }
  } catch (error) {
    console.error('Email error:', error)
    return { success: false, error }
  }
}

// Email Templates
export const emailTemplates = {
  orderConfirmation: (orderNumber: string, customerName: string, total: number) => ({
    subject: `Order Confirmation - ${orderNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #1a1a1a; text-align: center;">Thank You for Your Order!</h1>
        <p>Dear ${customerName},</p>
        <p>We've received your order and are preparing it for shipment. Here are your order details:</p>
        
        <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h2 style="margin: 0 0 10px 0;">Order #${orderNumber}</h2>
          <p style="margin: 0; font-size: 18px; font-weight: bold;">Total: $${total.toFixed(2)}</p>
        </div>
        
        <p>You'll receive a shipping confirmation email once your order is on its way.</p>
        <p>Blessings,<br>The Worship Leader Team</p>
        
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
          <p style="color: #666; font-size: 12px;">
            This email was sent from Worship Leader Ministry<br>
            If you have any questions, please contact <NAME_EMAIL>
          </p>
        </div>
      </div>
    `,
  }),

  contactFormSubmission: (name: string, email: string, subject: string, message: string) => ({
    subject: `New Contact Form Submission: ${subject}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #1a1a1a;">New Contact Form Submission</h1>
        
        <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Name:</strong> ${name}</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Subject:</strong> ${subject}</p>
        </div>
        
        <div style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
          <h3>Message:</h3>
          <p style="white-space: pre-wrap;">${message}</p>
        </div>
        
        <p style="margin-top: 20px; color: #666; font-size: 12px;">
          Sent from the Worship Leader website contact form
        </p>
      </div>
    `,
  }),

  newsletterWelcome: (name: string) => ({
    subject: 'Welcome to the Worship Leader Newsletter!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #1a1a1a; text-align: center;">Welcome to Our Ministry!</h1>
        <p>Dear ${name || 'Friend'},</p>
        
        <p>Thank you for subscribing to our newsletter! We're excited to have you join our community of believers.</p>
        
        <p>You'll receive updates about:</p>
        <ul>
          <li>New worship songs and albums</li>
          <li>Upcoming ministry events and concerts</li>
          <li>Inspirational devotionals and testimonies</li>
          <li>Exclusive content and early access to new releases</li>
        </ul>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="https://your-website.com/music" style="background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Listen to Our Latest Music
          </a>
        </div>
        
        <p>Blessings and peace,<br>The Worship Leader Team</p>
        
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
          <p style="color: #666; font-size: 12px;">
            You're receiving this because you subscribed to our newsletter.<br>
            <a href="https://your-website.com/unsubscribe" style="color: #666;">Unsubscribe</a>
          </p>
        </div>
      </div>
    `,
  }),
}
