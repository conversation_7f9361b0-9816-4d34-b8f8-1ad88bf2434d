'use client'

import { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import { Search, Filter, Grid3X3, List, Play, Shuffle } from 'lucide-react'
import { Track } from '@/types'
import TrackCard from './TrackCard'
import { useMusicStore } from '@/store/music'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'
import { debounce, sortBy as sortArray } from '@/lib/utils'

interface MusicGridProps {
  tracks: Track[]
  title?: string
  showSearch?: boolean
  showFilters?: boolean
}

type SortOption = 'newest' | 'oldest' | 'name' | 'duration'
type ViewMode = 'grid' | 'list'

export default function MusicGrid({ 
  tracks, 
  title = 'Music',
  showSearch = true,
  showFilters = true
}: MusicGridProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedGenre, setSelectedGenre] = useState<string>('all')
  const [sortBy, setSortBy] = useState<SortOption>('newest')
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  
  const { setPlaylist, playTrack } = useMusicStore()

  // Debounced search
  const debouncedSearch = useMemo(
    () => debounce((query: string) => setSearchQuery(query), 300),
    []
  )

  // Get unique genres
  const genres = useMemo(() => {
    const allGenres = tracks.flatMap(track => track.genre)
    return ['all', ...Array.from(new Set(allGenres))]
  }, [tracks])

  // Filter and sort tracks
  const filteredTracks = useMemo(() => {
    let filtered = tracks

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(track =>
        track.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        track.artist.toLowerCase().includes(searchQuery.toLowerCase()) ||
        track.album.toLowerCase().includes(searchQuery.toLowerCase()) ||
        track.genre.some(genre => genre.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Filter by genre
    if (selectedGenre !== 'all') {
      filtered = filtered.filter(track => track.genre.includes(selectedGenre))
    }

    // Sort tracks
    switch (sortBy) {
      case 'newest':
        filtered = sortArray(filtered, 'releaseDate', 'desc')
        break
      case 'oldest':
        filtered = sortArray(filtered, 'releaseDate', 'asc')
        break
      case 'name':
        filtered = sortArray(filtered, 'title', 'asc')
        break
      case 'duration':
        filtered = sortArray(filtered, 'duration', 'desc')
        break
      default:
        break
    }

    return filtered
  }, [tracks, searchQuery, selectedGenre, sortBy])

  const handlePlayAll = () => {
    if (filteredTracks.length > 0) {
      setPlaylist(filteredTracks)
      playTrack(filteredTracks[0], filteredTracks)
    }
  }

  const handleShuffle = () => {
    if (filteredTracks.length > 0) {
      const shuffled = [...filteredTracks].sort(() => Math.random() - 0.5)
      setPlaylist(shuffled)
      playTrack(shuffled[0], shuffled)
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        className="text-center"
        variants={ANIMATION_VARIANTS.slideUp}
        initial="hidden"
        animate="visible"
        transition={TRANSITIONS.default}
      >
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-4">
          {title} <span className="text-gradient">Collection</span>
        </h1>
        <p className="text-xl text-savage-white/80 max-w-2xl mx-auto">
          Explore the complete discography and discover your next favorite track
        </p>
      </motion.div>

      {/* Controls */}
      <motion.div
        className="space-y-6"
        variants={ANIMATION_VARIANTS.slideUp}
        initial="hidden"
        animate="visible"
        transition={{ ...TRANSITIONS.default, delay: 0.1 }}
      >
        {/* Search Bar */}
        {showSearch && (
          <div className="max-w-md mx-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="Search tracks, artists, albums..."
                onChange={(e) => debouncedSearch(e.target.value)}
                className="w-full px-4 py-3 pl-12 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
              />
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-savage-white/60" />
            </div>
          </div>
        )}

        {/* Filters and Actions */}
        <div className="flex flex-col lg:flex-row justify-between items-center gap-4">
          <div className="flex flex-wrap items-center gap-4">
            {/* Genre Filter */}
            {showFilters && (
              <select
                value={selectedGenre}
                onChange={(e) => setSelectedGenre(e.target.value)}
                className="px-4 py-2 glass rounded-lg text-savage-white focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
              >
                {genres.map((genre) => (
                  <option key={genre} value={genre}>
                    {genre === 'all' ? 'All Genres' : genre}
                  </option>
                ))}
              </select>
            )}

            {/* Sort Options */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as SortOption)}
              className="px-4 py-2 glass rounded-lg text-savage-white focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="name">Name A-Z</option>
              <option value="duration">Longest First</option>
            </select>
          </div>

          <div className="flex items-center gap-4">
            {/* Play Actions */}
            <div className="flex items-center space-x-2">
              <motion.button
                onClick={handlePlayAll}
                className="btn btn-primary"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Play className="w-4 h-4 mr-2" />
                Play All
              </motion.button>
              
              <motion.button
                onClick={handleShuffle}
                className="btn btn-secondary"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Shuffle className="w-4 h-4 mr-2" />
                Shuffle
              </motion.button>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded transition-colors duration-300 ${
                  viewMode === 'grid' 
                    ? 'bg-savage-yellow text-savage-black' 
                    : 'text-savage-white hover:text-savage-yellow'
                }`}
              >
                <Grid3X3 className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded transition-colors duration-300 ${
                  viewMode === 'list' 
                    ? 'bg-savage-yellow text-savage-black' 
                    : 'text-savage-white hover:text-savage-yellow'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>

            {/* Results Count */}
            <span className="text-sm text-savage-white/60">
              {filteredTracks.length} tracks
            </span>
          </div>
        </div>
      </motion.div>

      {/* Tracks Grid/List */}
      <motion.div
        className={`${
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' 
            : 'space-y-2'
        }`}
        variants={ANIMATION_VARIANTS.stagger}
        initial="hidden"
        animate="visible"
      >
        {filteredTracks.length > 0 ? (
          filteredTracks.map((track, index) => (
            <TrackCard
              key={track.id}
              track={track}
              index={index}
              showArtwork={viewMode === 'grid'}
              compact={viewMode === 'list'}
            />
          ))
        ) : (
          <motion.div
            className="col-span-full text-center py-16"
            variants={ANIMATION_VARIANTS.fadeIn}
          >
            <div className="text-6xl mb-4">🎵</div>
            <h3 className="text-xl font-semibold text-savage-white mb-2">
              No tracks found
            </h3>
            <p className="text-savage-white/60">
              Try adjusting your search or filter criteria
            </p>
          </motion.div>
        )}
      </motion.div>
    </div>
  )
}
