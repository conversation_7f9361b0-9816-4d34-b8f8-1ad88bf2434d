'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { Play, Pause, SkipB<PERSON>, Ski<PERSON>For<PERSON>, Volume2, Shuffle, Repeat, ChevronUp, ChevronDown } from 'lucide-react'
import Image from 'next/image'
import { useMusicStore } from '@/store/music'
import { formatDuration } from '@/lib/utils'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'

export default function MusicPlayer() {
  const {
    currentTrack,
    isPlaying,
    volume,
    currentTime,
    duration,
    shuffle,
    repeat,
    isPlayerVisible,
    pauseTrack,
    resumeTrack,
    nextTrack,
    previousTrack,
    setVolume,
    toggleShuffle,
    toggleRepeat,
    togglePlayer,
    seekTo,
  } = useMusicStore()

  if (!currentTrack) return null

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const newTime = (clickX / rect.width) * duration
    seekTo(newTime)
  }

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setVolume(parseFloat(e.target.value))
  }

  return (
    <AnimatePresence>
      {isPlayerVisible && (
        <motion.div
          className="fixed bottom-0 left-0 right-0 z-40 glass backdrop-blur-md border-t border-glass-border"
          initial={{ y: '100%' }}
          animate={{ y: 0 }}
          exit={{ y: '100%' }}
          transition={TRANSITIONS.default}
        >
          {/* Collapse/Expand Button */}
          <button
            onClick={togglePlayer}
            className="absolute -top-8 left-1/2 transform -translate-x-1/2 p-2 glass rounded-t-lg hover:bg-glass-light transition-colors duration-300"
          >
            <ChevronDown className="w-4 h-4 text-savage-white" />
          </button>

          <div className="container py-4">
            <div className="flex items-center space-x-4">
              {/* Track Info */}
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <div className="relative w-12 h-12 rounded-lg overflow-hidden">
                  <Image
                    src={currentTrack.artwork}
                    alt={currentTrack.title}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="min-w-0 flex-1">
                  <h4 className="text-sm font-medium text-savage-white truncate">
                    {currentTrack.title}
                  </h4>
                  <p className="text-xs text-savage-white/60 truncate">
                    {currentTrack.artist}
                  </p>
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={toggleShuffle}
                  className={`p-2 rounded-lg transition-colors duration-300 ${
                    shuffle ? 'bg-savage-yellow text-savage-black' : 'hover:bg-glass-light text-savage-white'
                  }`}
                >
                  <Shuffle className="w-4 h-4" />
                </button>

                <button
                  onClick={previousTrack}
                  className="p-2 hover:bg-glass-light rounded-lg transition-colors duration-300"
                >
                  <SkipBack className="w-5 h-5 text-savage-white" />
                </button>

                <motion.button
                  onClick={isPlaying ? pauseTrack : resumeTrack}
                  className="p-3 bg-gradient-primary rounded-full hover:bg-gradient-primary-hover transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {isPlaying ? (
                    <Pause className="w-5 h-5 text-savage-black" />
                  ) : (
                    <Play className="w-5 h-5 text-savage-black ml-0.5" />
                  )}
                </motion.button>

                <button
                  onClick={nextTrack}
                  className="p-2 hover:bg-glass-light rounded-lg transition-colors duration-300"
                >
                  <SkipForward className="w-5 h-5 text-savage-white" />
                </button>

                <button
                  onClick={toggleRepeat}
                  className={`p-2 rounded-lg transition-colors duration-300 ${
                    repeat !== 'none' ? 'bg-savage-yellow text-savage-black' : 'hover:bg-glass-light text-savage-white'
                  }`}
                >
                  <Repeat className="w-4 h-4" />
                  {repeat === 'one' && (
                    <span className="absolute -top-1 -right-1 w-2 h-2 bg-savage-red rounded-full" />
                  )}
                </button>
              </div>

              {/* Progress */}
              <div className="flex-1 max-w-md">
                <div className="flex items-center space-x-2 text-xs text-savage-white/60 mb-1">
                  <span>{formatDuration(Math.floor(currentTime))}</span>
                  <span>{formatDuration(Math.floor(duration))}</span>
                </div>
                <div
                  className="w-full h-1 bg-savage-gray rounded-full cursor-pointer"
                  onClick={handleProgressClick}
                >
                  <div
                    className="h-full bg-gradient-primary rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  />
                </div>
              </div>

              {/* Volume */}
              <div className="flex items-center space-x-2">
                <Volume2 className="w-4 h-4 text-savage-white" />
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={volume}
                  onChange={handleVolumeChange}
                  className="w-20 h-1 bg-savage-gray rounded-full appearance-none cursor-pointer slider"
                />
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
