'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { Play, Pause, Heart, Share2, Download } from 'lucide-react'
import { Track } from '@/types'
import { useMusicStore } from '@/store/music'
import { formatDuration, formatDate } from '@/lib/utils'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'

interface TrackCardProps {
  track: Track
  index?: number
  showArtwork?: boolean
  compact?: boolean
}

export default function TrackCard({ 
  track, 
  index = 0, 
  showArtwork = true, 
  compact = false 
}: TrackCardProps) {
  const { currentTrack, isPlaying, playTrack, pauseTrack, resumeTrack } = useMusicStore()
  
  const isCurrentTrack = currentTrack?.id === track.id
  const isCurrentlyPlaying = isCurrentTrack && isPlaying

  const handlePlayPause = () => {
    if (isCurrentTrack) {
      if (isPlaying) {
        pauseTrack()
      } else {
        resumeTrack()
      }
    } else {
      playTrack(track)
    }
  }

  if (compact) {
    return (
      <motion.div
        className="flex items-center space-x-4 p-4 glass rounded-lg hover:bg-glass-light transition-all duration-300 group"
        variants={ANIMATION_VARIANTS.slideUp}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        transition={{ ...TRANSITIONS.default, delay: index * 0.1 }}
      >
        {/* Track Number / Play Button */}
        <div className="relative w-8 h-8 flex items-center justify-center">
          <span className={`text-sm font-medium transition-opacity duration-300 ${
            isCurrentlyPlaying ? 'opacity-0' : 'group-hover:opacity-0'
          }`}>
            {index + 1}
          </span>
          <motion.button
            onClick={handlePlayPause}
            className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300 ${
              isCurrentlyPlaying ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
            }`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            {isCurrentlyPlaying ? (
              <Pause className="w-4 h-4 text-savage-yellow" />
            ) : (
              <Play className="w-4 h-4 text-savage-yellow" />
            )}
          </motion.button>
        </div>

        {/* Track Info */}
        <div className="flex-1 min-w-0">
          <h4 className={`font-medium truncate transition-colors duration-300 ${
            isCurrentTrack ? 'text-savage-yellow' : 'text-savage-white group-hover:text-savage-yellow'
          }`}>
            {track.title}
          </h4>
          <p className="text-sm text-savage-white/60 truncate">
            {track.artist}
          </p>
        </div>

        {/* Duration */}
        <div className="text-sm text-savage-white/60">
          {formatDuration(track.duration)}
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button className="p-1 hover:text-savage-yellow transition-colors duration-300">
            <Heart className="w-4 h-4" />
          </button>
          <button className="p-1 hover:text-savage-yellow transition-colors duration-300">
            <Share2 className="w-4 h-4" />
          </button>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      className="card-hover group"
      variants={ANIMATION_VARIANTS.slideUp}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      transition={{ ...TRANSITIONS.default, delay: index * 0.1 }}
    >
      {/* Artwork */}
      {showArtwork && (
        <div className="relative aspect-square mb-4 overflow-hidden rounded-lg">
          <Image
            src={track.artwork}
            alt={track.title}
            fill
            className="object-cover transition-transform duration-700 group-hover:scale-110"
          />
          
          {/* Play Button Overlay */}
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <motion.button
              onClick={handlePlayPause}
              className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center shadow-glow"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              {isCurrentlyPlaying ? (
                <Pause className="w-6 h-6 text-savage-black" />
              ) : (
                <Play className="w-6 h-6 text-savage-black ml-1" />
              )}
            </motion.button>
          </div>

          {/* Featured Badge */}
          {track.featured && (
            <div className="absolute top-3 left-3">
              <span className="px-2 py-1 bg-savage-yellow text-savage-black text-xs font-bold rounded">
                Featured
              </span>
            </div>
          )}

          {/* Now Playing Indicator */}
          {isCurrentlyPlaying && (
            <div className="absolute top-3 right-3">
              <div className="flex items-center space-x-1">
                <div className="w-1 h-4 bg-savage-yellow rounded animate-pulse" />
                <div className="w-1 h-6 bg-savage-yellow rounded animate-pulse delay-100" />
                <div className="w-1 h-4 bg-savage-yellow rounded animate-pulse delay-200" />
              </div>
            </div>
          )}
        </div>
      )}

      {/* Track Info */}
      <div className="space-y-3">
        <div>
          <h3 className={`text-lg font-semibold transition-colors duration-300 ${
            isCurrentTrack ? 'text-savage-yellow' : 'text-savage-white group-hover:text-savage-yellow'
          }`}>
            {track.title}
          </h3>
          <p className="text-savage-white/60">
            {track.artist} • {track.album}
          </p>
        </div>

        {/* Metadata */}
        <div className="flex items-center justify-between text-sm text-savage-white/60">
          <span>{formatDate(track.releaseDate)}</span>
          <span>{formatDuration(track.duration)}</span>
        </div>

        {/* Genre Tags */}
        <div className="flex flex-wrap gap-2">
          {track.genre.map((genre) => (
            <span
              key={genre}
              className="px-2 py-1 bg-savage-gray text-savage-white/80 text-xs rounded"
            >
              {genre}
            </span>
          ))}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center space-x-3">
            <motion.button
              onClick={handlePlayPause}
              className="btn btn-primary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {isCurrentlyPlaying ? (
                <>
                  <Pause className="w-4 h-4 mr-2" />
                  Pause
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  Play
                </>
              )}
            </motion.button>
          </div>

          <div className="flex items-center space-x-2">
            <button className="p-2 hover:bg-glass-light rounded-lg transition-colors duration-300">
              <Heart className="w-4 h-4 text-savage-white hover:text-savage-red" />
            </button>
            <button className="p-2 hover:bg-glass-light rounded-lg transition-colors duration-300">
              <Share2 className="w-4 h-4 text-savage-white hover:text-savage-yellow" />
            </button>
            <button className="p-2 hover:bg-glass-light rounded-lg transition-colors duration-300">
              <Download className="w-4 h-4 text-savage-white hover:text-savage-yellow" />
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
