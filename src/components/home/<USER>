'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Mail, Send, Check, AlertCircle } from 'lucide-react'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'
import { isValidEmail } from '@/lib/utils'

export default function NewsletterSection() {
  const [email, setEmail] = useState('')
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [message, setMessage] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!isValidEmail(email)) {
      setStatus('error')
      setMessage('Please enter a valid email address')
      return
    }

    setStatus('loading')
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Mock success response
      setStatus('success')
      setMessage('Successfully subscribed! Check your email for confirmation.')
      setEmail('')
    } catch (error) {
      setStatus('error')
      setMessage('Something went wrong. Please try again.')
    }
  }

  return (
    <section className="section-padding bg-gradient-to-br from-savage-black via-savage-dark to-savage-black relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-savage-yellow via-savage-red to-savage-yellow animate-pulse" />
      </div>

      <div className="container relative z-10">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          variants={ANIMATION_VARIANTS.stagger}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {/* Header */}
          <motion.div
            className="mb-12"
            variants={ANIMATION_VARIANTS.slideUp}
            transition={TRANSITIONS.default}
          >
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-6">
              Stay <span className="text-gradient">Updated</span>
            </h2>
            <p className="text-xl text-savage-white/80 max-w-2xl mx-auto leading-relaxed">
              Be the first to know about new releases, exclusive content, 
              upcoming shows, and special merchandise drops.
            </p>
          </motion.div>

          {/* Newsletter Form */}
          <motion.div
            className="max-w-md mx-auto"
            variants={ANIMATION_VARIANTS.scale}
            transition={{ ...TRANSITIONS.default, delay: 0.2 }}
          >
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="relative">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  className="w-full px-6 py-4 glass rounded-xl text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0 text-lg"
                  disabled={status === 'loading'}
                  required
                />
                <Mail className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-savage-white/60" />
              </div>

              <motion.button
                type="submit"
                disabled={status === 'loading' || status === 'success'}
                className="w-full btn btn-primary text-lg py-4 disabled:opacity-50 disabled:cursor-not-allowed"
                whileHover={status === 'idle' ? { scale: 1.02 } : {}}
                whileTap={status === 'idle' ? { scale: 0.98 } : {}}
              >
                {status === 'loading' ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-5 h-5 border-2 border-savage-black border-t-transparent rounded-full animate-spin" />
                    <span>Subscribing...</span>
                  </div>
                ) : status === 'success' ? (
                  <div className="flex items-center justify-center space-x-2">
                    <Check className="w-5 h-5" />
                    <span>Subscribed!</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <Send className="w-5 h-5" />
                    <span>Subscribe Now</span>
                  </div>
                )}
              </motion.button>
            </form>

            {/* Status Message */}
            {message && (
              <motion.div
                className={`mt-4 p-4 rounded-lg flex items-center space-x-2 ${
                  status === 'success' 
                    ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                    : 'bg-red-500/20 text-red-400 border border-red-500/30'
                }`}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={TRANSITIONS.fast}
              >
                {status === 'success' ? (
                  <Check className="w-5 h-5 flex-shrink-0" />
                ) : (
                  <AlertCircle className="w-5 h-5 flex-shrink-0" />
                )}
                <span className="text-sm">{message}</span>
              </motion.div>
            )}
          </motion.div>

          {/* Benefits */}
          <motion.div
            className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={ANIMATION_VARIANTS.stagger}
            transition={{ ...TRANSITIONS.default, delay: 0.4 }}
          >
            {[
              {
                icon: '🎵',
                title: 'New Releases',
                description: 'Be the first to hear new tracks and albums'
              },
              {
                icon: '🎫',
                title: 'Exclusive Access',
                description: 'Early access to tickets and special events'
              },
              {
                icon: '👕',
                title: 'Merch Drops',
                description: 'Get notified about limited edition merchandise'
              }
            ].map((benefit, index) => (
              <motion.div
                key={benefit.title}
                className="text-center"
                variants={ANIMATION_VARIANTS.slideUp}
                transition={{ ...TRANSITIONS.default, delay: index * 0.1 }}
              >
                <div className="text-4xl mb-4">{benefit.icon}</div>
                <h3 className="text-lg font-semibold text-savage-white mb-2">
                  {benefit.title}
                </h3>
                <p className="text-savage-white/70">
                  {benefit.description}
                </p>
              </motion.div>
            ))}
          </motion.div>

          {/* Privacy Note */}
          <motion.p
            className="mt-8 text-sm text-savage-white/50"
            variants={ANIMATION_VARIANTS.fadeIn}
            transition={{ ...TRANSITIONS.default, delay: 0.6 }}
          >
            We respect your privacy. Unsubscribe at any time.
          </motion.p>
        </motion.div>
      </div>
    </section>
  )
}
