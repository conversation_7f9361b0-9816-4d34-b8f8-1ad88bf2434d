'use client'

import { useEffect, useRef } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Play, ChevronDown } from 'lucide-react'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'
import { scrollToElement } from '@/lib/utils'

export default function HeroSection() {
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    // Ensure video plays on load
    if (videoRef.current) {
      videoRef.current.play().catch(console.error)
    }
  }, [])

  const handleScrollToFeatured = () => {
    scrollToElement('featured', 80)
  }

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Video Background */}
      <div className="absolute inset-0 z-0">
        <video
          ref={videoRef}
          autoPlay
          muted
          loop
          playsInline
          className="w-full h-full object-cover"
        >
          <source
            src="https://assets.mixkit.co/videos/preview/mixkit-concert-lights-during-a-concert-4115-large.mp4"
            type="video/mp4"
          />
        </video>
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-overlay" />
        
        {/* Animated particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-savage-yellow rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -100, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center px-4 max-w-4xl mx-auto">
        <motion.div
          variants={ANIMATION_VARIANTS.stagger}
          initial="hidden"
          animate="visible"
        >
          {/* Main Title with Glitch Effect */}
          <motion.h1
            className="glitch text-4xl md:text-6xl lg:text-7xl font-heading font-bold mb-6"
            data-text="ACE MONET"
            variants={ANIMATION_VARIANTS.scale}
            transition={TRANSITIONS.slow}
          >
            ACE MONET
          </motion.h1>

          {/* Tagline */}
          <motion.p
            className="text-xl md:text-2xl lg:text-3xl text-savage-white/90 mb-8 font-light tracking-wide"
            variants={ANIMATION_VARIANTS.slideUp}
            transition={{ ...TRANSITIONS.default, delay: 0.2 }}
          >
            Inspiring Hearts Through Music, One Song at a Time
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
            variants={ANIMATION_VARIANTS.slideUp}
            transition={{ ...TRANSITIONS.default, delay: 0.4 }}
          >
            <Link
              href="/music"
              className="btn btn-primary text-lg px-8 py-4 group"
            >
              <Play className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
              Worship Now
            </Link>
            <button
              onClick={handleScrollToFeatured}
              className="btn btn-secondary text-lg px-8 py-4"
            >
              Latest Album
            </button>
          </motion.div>

          {/* Social Proof */}
          <motion.div
            className="flex flex-wrap justify-center items-center gap-8 text-savage-white/60 text-sm"
            variants={ANIMATION_VARIANTS.fadeIn}
            transition={{ ...TRANSITIONS.default, delay: 0.6 }}
          >
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-savage-yellow rounded-full animate-pulse" />
              <span>500K+ Streams</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-savage-red rounded-full animate-pulse" />
              <span>25+ Churches</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-savage-yellow rounded-full animate-pulse" />
              <span>50K+ Worshippers</span>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.button
        onClick={handleScrollToFeatured}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ ...TRANSITIONS.default, delay: 1 }}
      >
        <div className="flex flex-col items-center space-y-2 text-savage-white/80 hover:text-savage-yellow transition-colors duration-300">
          <span className="text-sm font-medium">Scroll Down</span>
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <ChevronDown className="w-6 h-6" />
          </motion.div>
        </div>
      </motion.button>

      {/* Audio Visualizer Effect */}
      <div className="absolute bottom-0 left-0 right-0 h-32 z-10 pointer-events-none">
        <div className="flex items-end justify-center space-x-1 h-full">
          {[...Array(50)].map((_, i) => (
            <motion.div
              key={i}
              className="bg-gradient-primary w-1 rounded-t"
              animate={{
                height: [
                  `${Math.random() * 20 + 10}%`,
                  `${Math.random() * 60 + 20}%`,
                  `${Math.random() * 20 + 10}%`,
                ],
              }}
              transition={{
                duration: 0.5 + Math.random() * 0.5,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
            />
          ))}
        </div>
      </div>
    </section>
  )
}
