'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { MapPin, Clock, Calendar, ExternalLink, Ticket } from 'lucide-react'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'
import { formatDate, formatDateTime } from '@/lib/utils'

// Mock data - replace with actual data from your API
const upcomingShows = [
  {
    id: '1',
    title: 'Worship Night Live',
    venue: 'First Baptist Church',
    city: 'Nashville',
    state: 'TN',
    country: 'USA',
    date: new Date('2024-08-15T19:00:00'),
    time: '7:00 PM',
    ticketUrl: 'https://tickets.example.com/worship-night-live',
    price: 15,
    soldOut: false,
    featured: true,
  },
  {
    id: '2',
    title: 'Faith & Music Festival',
    venue: 'Community Center Auditorium',
    city: 'Dallas',
    state: 'TX',
    country: 'USA',
    date: new Date('2024-09-22T18:30:00'),
    time: '6:30 PM',
    ticketUrl: 'https://tickets.example.com/faith-music-festival',
    price: 25,
    soldOut: false,
    featured: false,
  },
  {
    id: '3',
    title: 'Christmas Worship Concert',
    venue: 'Grace Community Church',
    city: 'Atlanta',
    state: 'GA',
    country: 'USA',
    date: new Date('2024-12-15T19:00:00'),
    time: '7:00 PM',
    ticketUrl: 'https://tickets.example.com/christmas-worship',
    price: 20,
    soldOut: true,
    featured: false,
  },
]

export default function UpcomingShows() {
  return (
    <section className="section-padding bg-savage-dark">
      <div className="container">
        <motion.div
          className="text-center mb-16"
          variants={ANIMATION_VARIANTS.slideUp}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          transition={TRANSITIONS.default}
        >
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-4">
            Upcoming <span className="text-gradient">Events</span>
          </h2>
          <p className="text-xl text-savage-white/80 max-w-2xl mx-auto">
            Join us for worship and fellowship - reserve your spot for these special ministry events
          </p>
        </motion.div>

        <motion.div
          className="grid gap-6 max-w-4xl mx-auto"
          variants={ANIMATION_VARIANTS.stagger}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {upcomingShows.map((show, index) => (
            <motion.div
              key={show.id}
              className={`card-hover ${show.featured ? 'ring-2 ring-savage-yellow/30' : ''}`}
              variants={ANIMATION_VARIANTS.slideUp}
              transition={{ ...TRANSITIONS.default, delay: index * 0.1 }}
            >
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                {/* Date Display */}
                <div className="flex items-center space-x-6">
                  <div className="text-center min-w-[80px]">
                    <div className="text-3xl font-heading font-bold text-savage-yellow">
                      {show.date.getDate()}
                    </div>
                    <div className="text-sm font-medium text-savage-white/80 uppercase tracking-wider">
                      {show.date.toLocaleDateString('en-US', { month: 'short' })}
                    </div>
                    <div className="text-xs text-savage-white/60">
                      {show.date.getFullYear()}
                    </div>
                  </div>

                  {/* Show Info */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-xl font-semibold text-savage-white">
                        {show.title}
                      </h3>
                      {show.featured && (
                        <span className="px-2 py-1 bg-savage-yellow text-savage-black text-xs font-bold rounded uppercase">
                          Featured
                        </span>
                      )}
                      {show.soldOut && (
                        <span className="px-2 py-1 bg-savage-red text-savage-white text-xs font-bold rounded uppercase">
                          Sold Out
                        </span>
                      )}
                    </div>

                    <div className="space-y-1 text-savage-white/70">
                      <div className="flex items-center space-x-2">
                        <MapPin className="w-4 h-4" />
                        <span>
                          {show.venue}, {show.city}
                          {show.state && `, ${show.state}`}, {show.country}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4" />
                        <span>{show.time}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Price and Action */}
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-lg font-semibold text-savage-yellow">
                      ${show.price}
                    </div>
                    <div className="text-xs text-savage-white/60">
                      Starting at
                    </div>
                  </div>

                  {show.soldOut ? (
                    <button
                      disabled
                      className="btn bg-savage-gray text-savage-white/60 cursor-not-allowed"
                    >
                      <Ticket className="w-4 h-4 mr-2" />
                      Sold Out
                    </button>
                  ) : (
                    <motion.a
                      href={show.ticketUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn btn-primary"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Ticket className="w-4 h-4 mr-2" />
                      Get Tickets
                      <ExternalLink className="w-4 h-4 ml-2" />
                    </motion.a>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* View All Shows */}
        <motion.div
          className="text-center mt-12"
          variants={ANIMATION_VARIANTS.fadeIn}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          transition={{ ...TRANSITIONS.default, delay: 0.4 }}
        >
          <Link
            href="/shows"
            className="btn btn-secondary"
          >
            <Calendar className="w-5 h-5 mr-2" />
            View All Shows
          </Link>
        </motion.div>
      </div>
    </section>
  )
}
