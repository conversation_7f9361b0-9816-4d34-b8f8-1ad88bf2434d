'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { Play, ExternalLink, Calendar, Music } from 'lucide-react'
import { ANIMATION_VARIANTS, TRANSITIONS, STREAMING_PLATFORMS } from '@/lib/constants'
import { formatDate } from '@/lib/utils'
import { useMusicStore } from '@/store/music'

// Mock data - replace with actual data from your API
const featuredRelease = {
  id: '1',
  title: 'Amazing Grace (New Arrangement)',
  artist: 'Worship Leader',
  album: 'Worship Sessions',
  artwork: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
  releaseDate: new Date('2024-07-25'),
  duration: 285,
  url: '/audio/amazing-grace.mp3',
  genre: ['Contemporary Christian', 'Worship'],
  description: 'A powerful new arrangement of the beloved hymn <PERSON> Grace, featuring modern instrumentation while honoring the timeless message of <PERSON>&rsquo;s incredible grace and love.',
  streamingLinks: [
    { platform: 'spotify' as const, url: 'https://open.spotify.com/track/example' },
    { platform: 'apple' as const, url: 'https://music.apple.com/track/example' },
    { platform: 'soundcloud' as const, url: 'https://soundcloud.com/track/example' },
  ]
}

export default function FeaturedRelease() {
  const { playTrack } = useMusicStore()

  const handlePlayTrack = () => {
    playTrack(featuredRelease)
  }

  return (
    <section id="featured" className="section-padding bg-gradient-to-br from-savage-black via-savage-dark to-savage-black">
      <div className="container">
        <motion.div
          className="text-center mb-16"
          variants={ANIMATION_VARIANTS.slideUp}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          transition={TRANSITIONS.default}
        >
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-4">
            Latest <span className="text-gradient">Release</span>
          </h2>
          <p className="text-xl text-savage-white/80 max-w-2xl mx-auto">
            Check out the newest track that&rsquo;s blessing hearts around the world
          </p>
        </motion.div>

        <motion.div
          className="max-w-6xl mx-auto"
          variants={ANIMATION_VARIANTS.stagger}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Artwork */}
            <motion.div
              className="relative group"
              variants={ANIMATION_VARIANTS.scale}
              transition={TRANSITIONS.slow}
            >
              <div className="relative aspect-square rounded-2xl overflow-hidden shadow-glass">
                <Image
                  src={featuredRelease.artwork}
                  alt={featuredRelease.title}
                  fill
                  className="object-cover transition-transform duration-700 group-hover:scale-110"
                  priority
                />
                
                {/* Play Button Overlay */}
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <motion.button
                    onClick={handlePlayTrack}
                    className="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center shadow-glow"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Play className="w-8 h-8 text-savage-black ml-1" />
                  </motion.button>
                </div>

                {/* Badge */}
                <div className="absolute top-4 left-4">
                  <span className="px-3 py-1 bg-savage-red text-savage-white text-sm font-semibold rounded-full">
                    New Worship Song
                  </span>
                </div>

                {/* Floating Elements */}
                <div className="absolute -top-4 -right-4 w-8 h-8 bg-savage-yellow rounded-full animate-float" />
                <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-savage-red rounded-full animate-float delay-300" />
              </div>
            </motion.div>

            {/* Release Info */}
            <motion.div
              className="space-y-6"
              variants={ANIMATION_VARIANTS.slideLeft}
              transition={{ ...TRANSITIONS.default, delay: 0.2 }}
            >
              <div>
                <h3 className="text-3xl md:text-4xl font-heading font-bold text-savage-white mb-2">
                  {featuredRelease.title}
                </h3>
                <p className="text-xl text-savage-yellow font-medium mb-4">
                  by {featuredRelease.artist}
                </p>
                
                {/* Release Details */}
                <div className="flex flex-wrap gap-4 text-savage-white/70 mb-6">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4" />
                    <span>Released: {formatDate(featuredRelease.releaseDate)}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Music className="w-4 h-4" />
                    <span>{featuredRelease.genre.join(', ')}</span>
                  </div>
                </div>
              </div>

              <p className="text-savage-white/80 leading-relaxed text-lg">
                {featuredRelease.description}
              </p>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-4">
                <motion.button
                  onClick={handlePlayTrack}
                  className="btn btn-primary"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Play className="w-5 h-5 mr-2" />
                  Play Now
                </motion.button>
                
                <Link
                  href="/music"
                  className="btn btn-secondary"
                >
                  <Music className="w-5 h-5 mr-2" />
                  View All Music
                </Link>
              </div>

              {/* Streaming Links */}
              <div className="pt-6 border-t border-glass-border">
                <h4 className="text-lg font-semibold text-savage-white mb-4">
                  Listen on your favorite platform
                </h4>
                <div className="flex flex-wrap gap-3">
                  {featuredRelease.streamingLinks.map((link) => {
                    const platform = STREAMING_PLATFORMS.find(p => p.value === link.platform)
                    if (!platform) return null

                    return (
                      <motion.a
                        key={link.platform}
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-2 px-4 py-2 glass rounded-lg hover:bg-glass-light transition-all duration-300 group"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <div 
                          className="w-5 h-5 rounded"
                          style={{ backgroundColor: platform.color }}
                        />
                        <span className="text-savage-white group-hover:text-savage-yellow transition-colors duration-300">
                          {platform.name}
                        </span>
                        <ExternalLink className="w-4 h-4 text-savage-white/60 group-hover:text-savage-yellow transition-colors duration-300" />
                      </motion.a>
                    )
                  })}
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
