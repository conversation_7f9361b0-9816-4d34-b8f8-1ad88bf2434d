'use client'

import { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, SlidersHorizontal, Grid3X3, List } from 'lucide-react'
import { Product, ProductCategory } from '@/types'
import ProductCard from './ProductCard'
import CategoryFilter from './CategoryFilter'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'
import { debounce, filterBy, sortBy as sortArray } from '@/lib/utils'

interface ProductGridProps {
  products: Product[]
  title?: string
  showFilters?: boolean
  showSearch?: boolean
  defaultCategory?: ProductCategory
}

type SortOption = 'name' | 'price-low' | 'price-high' | 'newest' | 'popular'
type ViewMode = 'grid' | 'list'

export default function ProductGrid({ 
  products, 
  title = 'Products',
  showFilters = true,
  showSearch = true,
  defaultCategory = 'all'
}: ProductGridProps) {
  const [selectedCategory, setSelectedCategory] = useState<ProductCategory>(defaultCategory)
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState<SortOption>('newest')
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [showFiltersPanel, setShowFiltersPanel] = useState(false)

  // Debounced search
  const debouncedSearch = useMemo(
    () => debounce((query: string) => setSearchQuery(query), 300),
    []
  )

  // Filter and sort products
  const filteredProducts = useMemo(() => {
    let filtered = products

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory)
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Sort products
    switch (sortBy) {
      case 'name':
        filtered = sortArray(filtered, 'name', 'asc')
        break
      case 'price-low':
        filtered = sortArray(filtered, 'price', 'asc')
        break
      case 'price-high':
        filtered = sortArray(filtered, 'price', 'desc')
        break
      case 'newest':
        filtered = sortArray(filtered, 'createdAt', 'desc')
        break
      case 'popular':
        // Sort by featured first, then by creation date
        filtered = filtered.sort((a, b) => {
          if (a.featured && !b.featured) return -1
          if (!a.featured && b.featured) return 1
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        })
        break
      default:
        break
    }

    return filtered
  }, [products, selectedCategory, searchQuery, sortBy])

  // Calculate product counts by category
  const productCounts = useMemo(() => {
    const counts: Record<ProductCategory, number> = {
      all: products.length,
      clothing: 0,
      music: 0,
      accessories: 0,
      bundles: 0,
    }

    products.forEach(product => {
      counts[product.category]++
    })

    return counts
  }, [products])

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        className="text-center"
        variants={ANIMATION_VARIANTS.slideUp}
        initial="hidden"
        animate="visible"
        transition={TRANSITIONS.default}
      >
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-4">
          {title} <span className="text-gradient">Store</span>
        </h1>
        <p className="text-xl text-savage-white/80 max-w-2xl mx-auto">
          Discover exclusive merchandise, music, and limited edition items
        </p>
      </motion.div>

      {/* Filters and Search */}
      {(showFilters || showSearch) && (
        <motion.div
          className="space-y-6"
          variants={ANIMATION_VARIANTS.slideUp}
          initial="hidden"
          animate="visible"
          transition={{ ...TRANSITIONS.default, delay: 0.1 }}
        >
          {/* Search Bar */}
          {showSearch && (
            <div className="max-w-md mx-auto">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search products..."
                  onChange={(e) => debouncedSearch(e.target.value)}
                  className="w-full px-4 py-3 pl-12 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                />
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-savage-white/60" />
              </div>
            </div>
          )}

          {/* Category Filters */}
          {showFilters && (
            <CategoryFilter
              selectedCategory={selectedCategory}
              onCategoryChange={setSelectedCategory}
              productCounts={productCounts}
            />
          )}

          {/* Sort and View Options */}
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="flex items-center space-x-4">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortOption)}
                className="px-4 py-2 glass rounded-lg text-savage-white focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
              >
                <option value="newest">Newest First</option>
                <option value="popular">Most Popular</option>
                <option value="name">Name A-Z</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
              </select>

              <button
                onClick={() => setShowFiltersPanel(!showFiltersPanel)}
                className="sm:hidden btn btn-secondary"
              >
                <SlidersHorizontal className="w-4 h-4 mr-2" />
                Filters
              </button>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm text-savage-white/60">
                {filteredProducts.length} products
              </span>
              
              <div className="flex items-center space-x-1 ml-4">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded transition-colors duration-300 ${
                    viewMode === 'grid' 
                      ? 'bg-savage-yellow text-savage-black' 
                      : 'text-savage-white hover:text-savage-yellow'
                  }`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded transition-colors duration-300 ${
                    viewMode === 'list' 
                      ? 'bg-savage-yellow text-savage-black' 
                      : 'text-savage-white hover:text-savage-yellow'
                  }`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Products Grid */}
      <AnimatePresence mode="wait">
        <motion.div
          key={`${selectedCategory}-${searchQuery}-${sortBy}`}
          className={`grid gap-6 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
              : 'grid-cols-1'
          }`}
          variants={ANIMATION_VARIANTS.stagger}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          {filteredProducts.length > 0 ? (
            filteredProducts.map((product, index) => (
              <ProductCard
                key={product.id}
                product={product}
                index={index}
              />
            ))
          ) : (
            <motion.div
              className="col-span-full text-center py-16"
              variants={ANIMATION_VARIANTS.fadeIn}
            >
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold text-savage-white mb-2">
                No products found
              </h3>
              <p className="text-savage-white/60">
                Try adjusting your search or filter criteria
              </p>
            </motion.div>
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
