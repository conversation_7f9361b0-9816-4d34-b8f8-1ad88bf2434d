'use client'

import { motion } from 'framer-motion'
import { ProductCategory } from '@/types'
import { PRODUCT_CATEGORIES, ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'

interface CategoryFilterProps {
  selectedCategory: ProductCategory
  onCategoryChange: (category: ProductCategory) => void
  productCounts?: Record<ProductCategory, number>
}

export default function CategoryFilter({ 
  selectedCategory, 
  onCategoryChange, 
  productCounts 
}: CategoryFilterProps) {
  return (
    <motion.div
      className="flex flex-wrap gap-3 justify-center lg:justify-start"
      variants={ANIMATION_VARIANTS.stagger}
      initial="hidden"
      animate="visible"
    >
      {PRODUCT_CATEGORIES.map((category, index) => {
        const isActive = selectedCategory === category.value
        const count = productCounts?.[category.value] || 0

        return (
          <motion.button
            key={category.value}
            onClick={() => onCategoryChange(category.value)}
            className={`
              relative px-6 py-3 rounded-lg font-medium transition-all duration-300
              ${isActive 
                ? 'bg-gradient-primary text-savage-black shadow-glow' 
                : 'glass text-savage-white hover:bg-glass-light hover:text-savage-yellow'
              }
            `}
            variants={ANIMATION_VARIANTS.slideUp}
            transition={{ ...TRANSITIONS.default, delay: index * 0.1 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <span className="relative z-10">
              {category.label}
              {productCounts && count > 0 && (
                <span className={`ml-2 text-sm ${
                  isActive ? 'text-savage-black/70' : 'text-savage-white/60'
                }`}>
                  ({count})
                </span>
              )}
            </span>

            {/* Active indicator */}
            {isActive && (
              <motion.div
                className="absolute inset-0 bg-gradient-primary rounded-lg"
                layoutId="activeCategory"
                initial={false}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              />
            )}

            {/* Hover effect */}
            <div className={`
              absolute inset-0 rounded-lg opacity-0 transition-opacity duration-300
              ${!isActive ? 'group-hover:opacity-100 bg-gradient-glass' : ''}
            `} />
          </motion.button>
        )
      })}
    </motion.div>
  )
}
