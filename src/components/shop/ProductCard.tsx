'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { Heart, ShoppingCart, Eye, Star } from 'lucide-react'
import { Product } from '@/types'
import { useCartStore } from '@/store/cart'
import { formatPrice, calculateDiscount } from '@/lib/utils'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'

interface ProductCardProps {
  product: Product
  index?: number
}

export default function ProductCard({ product, index = 0 }: ProductCardProps) {
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [selectedColor, setSelectedColor] = useState(product.colors?.[0]?.value || '')
  const [selectedSize, setSelectedSize] = useState(product.sizes?.[0]?.value || '')
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  
  const { addItem } = useCartStore()

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    addItem(product, 1, {
      color: selectedColor,
      size: selectedSize,
    })
  }

  const handleWishlistToggle = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsWishlisted(!isWishlisted)
  }

  const handleQuickView = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    // TODO: Implement quick view modal
    console.log('Quick view:', product.id)
  }

  const discountPercentage = product.originalPrice 
    ? calculateDiscount(product.originalPrice, product.price)
    : 0

  return (
    <motion.div
      className="group relative"
      variants={ANIMATION_VARIANTS.slideUp}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      transition={{ ...TRANSITIONS.default, delay: index * 0.1 }}
    >
      <Link href={`/shop/products/${product.slug}`}>
        <div className="card-hover overflow-hidden">
          {/* Product Image */}
          <div className="relative aspect-square mb-4 overflow-hidden rounded-lg">
            <Image
              src={product.images[currentImageIndex]}
              alt={product.name}
              fill
              className="object-cover transition-transform duration-700 group-hover:scale-110"
            />

            {/* Badges */}
            <div className="absolute top-3 left-3 flex flex-col space-y-2">
              {product.badge && (
                <span className={`px-2 py-1 text-xs font-bold rounded uppercase ${
                  product.badge === 'new' ? 'bg-savage-yellow text-savage-black' :
                  product.badge === 'bestseller' ? 'bg-savage-red text-savage-white' :
                  product.badge === 'limited' ? 'bg-savage-red text-savage-white' :
                  'bg-savage-gray text-savage-white'
                }`}>
                  {product.badge}
                </span>
              )}
              {discountPercentage > 0 && (
                <span className="px-2 py-1 bg-savage-red text-savage-white text-xs font-bold rounded">
                  -{discountPercentage}%
                </span>
              )}
            </div>

            {/* Action Buttons */}
            <div className="absolute top-3 right-3 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <motion.button
                onClick={handleWishlistToggle}
                className={`p-2 rounded-lg backdrop-blur-md transition-colors duration-300 ${
                  isWishlisted 
                    ? 'bg-savage-red text-savage-white' 
                    : 'bg-black/30 text-savage-white hover:bg-savage-red'
                }`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Heart className={`w-4 h-4 ${isWishlisted ? 'fill-current' : ''}`} />
              </motion.button>

              <motion.button
                onClick={handleQuickView}
                className="p-2 bg-black/30 text-savage-white rounded-lg backdrop-blur-md hover:bg-savage-yellow hover:text-savage-black transition-colors duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Eye className="w-4 h-4" />
              </motion.button>
            </div>

            {/* Overlay */}
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

            {/* Quick Add to Cart */}
            <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <motion.button
                onClick={handleAddToCart}
                className="w-full btn btn-primary"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <ShoppingCart className="w-4 h-4 mr-2" />
                Add to Cart
              </motion.button>
            </div>

            {/* Image Navigation Dots */}
            {product.images.length > 1 && (
              <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-1">
                {product.images.map((_, i) => (
                  <button
                    key={i}
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      setCurrentImageIndex(i)
                    }}
                    className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                      i === currentImageIndex ? 'bg-savage-yellow' : 'bg-white/50'
                    }`}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="space-y-3">
            <div>
              <h3 className="font-semibold text-savage-white group-hover:text-savage-yellow transition-colors duration-300 line-clamp-2">
                {product.name}
              </h3>
              <p className="text-sm text-savage-white/60 mt-1">
                {product.category.charAt(0).toUpperCase() + product.category.slice(1)}
              </p>
            </div>

            {/* Price */}
            <div className="flex items-center space-x-2">
              <span className="text-lg font-bold text-savage-yellow">
                {formatPrice(product.price)}
              </span>
              {product.originalPrice && (
                <span className="text-sm text-savage-white/50 line-through">
                  {formatPrice(product.originalPrice)}
                </span>
              )}
            </div>

            {/* Colors */}
            {product.colors && product.colors.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-xs text-savage-white/60">Colors:</span>
                <div className="flex space-x-1">
                  {product.colors.slice(0, 4).map((color) => (
                    <button
                      key={color.value}
                      onClick={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                        setSelectedColor(color.value)
                      }}
                      className={`w-4 h-4 rounded-full border-2 transition-all duration-300 ${
                        selectedColor === color.value 
                          ? 'border-savage-yellow scale-110' 
                          : 'border-savage-white/30'
                      }`}
                      style={{ backgroundColor: color.hex }}
                      title={color.name}
                    />
                  ))}
                  {product.colors.length > 4 && (
                    <span className="text-xs text-savage-white/60">
                      +{product.colors.length - 4}
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Sizes */}
            {product.sizes && product.sizes.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-xs text-savage-white/60">Sizes:</span>
                <div className="flex space-x-1">
                  {product.sizes.slice(0, 4).map((size) => (
                    <button
                      key={size.value}
                      onClick={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                        setSelectedSize(size.value)
                      }}
                      className={`px-2 py-1 text-xs rounded border transition-all duration-300 ${
                        selectedSize === size.value
                          ? 'border-savage-yellow bg-savage-yellow text-savage-black'
                          : 'border-savage-white/30 text-savage-white/80'
                      } ${!size.inStock ? 'opacity-50 cursor-not-allowed' : ''}`}
                      disabled={!size.inStock}
                    >
                      {size.name}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Stock Status */}
            <div className="flex items-center justify-between">
              <div className={`text-xs ${
                product.stock > 10 ? 'text-green-400' :
                product.stock > 0 ? 'text-yellow-400' :
                'text-red-400'
              }`}>
                {product.stock > 10 ? 'In Stock' :
                 product.stock > 0 ? `Only ${product.stock} left` :
                 'Out of Stock'}
              </div>

              {/* Rating (if available) */}
              <div className="flex items-center space-x-1">
                <Star className="w-3 h-3 text-savage-yellow fill-current" />
                <span className="text-xs text-savage-white/60">4.8</span>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  )
}
