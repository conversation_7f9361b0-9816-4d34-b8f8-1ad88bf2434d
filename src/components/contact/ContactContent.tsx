'use client'

import { motion } from 'framer-motion'
import { Mail, MapPin, Phone, Clock, Instagram, Twitter, Youtube, Music } from 'lucide-react'
import ContactForm from '@/components/contact/ContactForm'
import { ANIMATION_VARIANTS, TRANSITIONS, SOCIAL_PLATFORMS } from '@/lib/constants'

const contactInfo = [
  {
    icon: Mail,
    title: 'Email',
    details: ['<EMAIL>', '<EMAIL>'],
    description: 'For ministry inquiries and event bookings'
  },
  {
    icon: MapPin,
    title: 'Location',
    details: ['Nashville, TN', 'United States'],
    description: 'Based in the heart of Christian music'
  },
  {
    icon: Phone,
    title: 'Phone',
    details: ['+****************'],
    description: 'Available Monday - Friday, 9AM - 6PM CST'
  },
  {
    icon: Clock,
    title: 'Response Time',
    details: ['24-48 hours'],
    description: 'We typically respond within 1-2 business days'
  }
]

const socialIcons = {
  instagram: Instagram,
  twitter: Twitter,
  youtube: Youtube,
  tiktok: Music,
  spotify: Music,
}

export default function ContactContent() {
  return (
    <div className="section-padding">
      <div className="container">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          variants={ANIMATION_VARIANTS.slideUp}
          initial="hidden"
          animate="visible"
          transition={TRANSITIONS.default}
        >
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-4">
            Contact <span className="text-gradient">Us</span>
          </h1>
          <p className="text-xl text-savage-white/80 max-w-2xl mx-auto">
            Ready to book a ministry event, collaborate, or just want to connect? 
            Let&rsquo;s spread God&rsquo;s love together through music.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-start">
          {/* Contact Form */}
          <ContactForm />

          {/* Contact Information */}
          <motion.div
            className="space-y-8"
            variants={ANIMATION_VARIANTS.stagger}
            initial="hidden"
            animate="visible"
            transition={{ ...TRANSITIONS.default, delay: 0.2 }}
          >
            {/* Contact Info Cards */}
            <div className="space-y-6">
              {contactInfo.map((info, index) => {
                const Icon = info.icon
                return (
                  <motion.div
                    key={info.title}
                    className="card"
                    variants={ANIMATION_VARIANTS.slideLeft}
                    transition={{ ...TRANSITIONS.default, delay: index * 0.1 }}
                  >
                    <div className="flex items-start space-x-4">
                      <div className="p-3 bg-gradient-primary rounded-lg">
                        <Icon className="w-6 h-6 text-savage-black" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-savage-white mb-2">
                          {info.title}
                        </h3>
                        <div className="space-y-1 mb-2">
                          {info.details.map((detail, i) => (
                            <p key={i} className="text-savage-yellow font-medium">
                              {detail}
                            </p>
                          ))}
                        </div>
                        <p className="text-sm text-savage-white/70">
                          {info.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </div>

            {/* Social Media */}
            <motion.div
              className="card"
              variants={ANIMATION_VARIANTS.slideLeft}
              transition={{ ...TRANSITIONS.default, delay: 0.4 }}
            >
              <h3 className="text-lg font-semibold text-savage-white mb-4">
                Follow the Ministry
              </h3>
              <p className="text-savage-white/70 mb-6">
                Stay connected and get the latest updates on our ministry
              </p>
              <div className="grid grid-cols-2 gap-4">
                {SOCIAL_PLATFORMS.map((platform) => {
                  const Icon = socialIcons[platform.value as keyof typeof socialIcons]
                  return (
                    <motion.a
                      key={platform.value}
                      href={platform.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-3 p-3 glass rounded-lg hover:bg-glass-light transition-all duration-300 group"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Icon className="w-5 h-5 text-savage-white group-hover:text-savage-yellow transition-colors duration-300" />
                      <span className="text-savage-white group-hover:text-savage-yellow transition-colors duration-300 capitalize">
                        {platform.name}
                      </span>
                    </motion.a>
                  )
                })}
              </div>
            </motion.div>

            {/* Quick Links */}
            <motion.div
              className="card"
              variants={ANIMATION_VARIANTS.slideLeft}
              transition={{ ...TRANSITIONS.default, delay: 0.5 }}
            >
              <h3 className="text-lg font-semibold text-savage-white mb-4">
                Ministry Resources
              </h3>
              <div className="space-y-3">
                {[
                  { label: 'Ministry Info', href: '/ministry-info' },
                  { label: 'Booking Details', href: '/booking' },
                  { label: 'Prayer Requests', href: '/prayer' },
                  { label: 'Testimonies', href: '/testimonies' },
                ].map((link) => (
                  <a
                    key={link.label}
                    href={link.href}
                    className="block text-savage-white/80 hover:text-savage-yellow transition-colors duration-300"
                  >
                    {link.label}
                  </a>
                ))}
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* FAQ Section */}
        <motion.div
          className="mt-20"
          variants={ANIMATION_VARIANTS.slideUp}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          transition={{ ...TRANSITIONS.default, delay: 0.3 }}
        >
          <h2 className="text-3xl font-heading font-bold text-center text-savage-white mb-12">
            Frequently Asked <span className="text-gradient">Questions</span>
          </h2>
          
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {[
              {
                question: 'How can I book your ministry for an event?',
                answer: 'Send us a booking inquiry through the contact form above <NAME_EMAIL> with your event details, date, and location.'
              },
              {
                question: 'Do you accept ministry collaboration requests?',
                answer: 'Yes! We&rsquo;re always open to partnering with other ministries. Please send us your proposal and we&rsquo;ll get back to you within 48 hours.'
              },
              {
                question: 'How can I request prayer?',
                answer: 'We would be honored to pray for you. Please use our contact form or email us directly with your prayer request.'
              },
              {
                question: 'What\'s the best way to stay updated on ministry events?',
                answer: 'Follow us on social media and subscribe to our newsletter for the latest ministry news, events, and exclusive content.'
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                className="card"
                variants={ANIMATION_VARIANTS.slideUp}
                transition={{ ...TRANSITIONS.default, delay: index * 0.1 }}
              >
                <h3 className="text-lg font-semibold text-savage-white mb-3">
                  {faq.question}
                </h3>
                <p className="text-savage-white/80">
                  {faq.answer}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  )
}
