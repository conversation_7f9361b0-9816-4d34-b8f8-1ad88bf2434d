'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Send, Mail, User, MessageSquare, Check, AlertCircle } from 'lucide-react'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'
import { isValidEmail } from '@/lib/utils'

interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
}

export default function ContactForm() {
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    subject: '',
    message: '',
  })
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [message, setMessage] = useState('')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation
    if (!formData.name.trim()) {
      setStatus('error')
      setMessage('Please enter your name')
      return
    }
    
    if (!isValidEmail(formData.email)) {
      setStatus('error')
      setMessage('Please enter a valid email address')
      return
    }
    
    if (!formData.subject.trim()) {
      setStatus('error')
      setMessage('Please enter a subject')
      return
    }
    
    if (!formData.message.trim()) {
      setStatus('error')
      setMessage('Please enter your message')
      return
    }

    setStatus('loading')
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Mock success response
      setStatus('success')
      setMessage('Message sent successfully! We&rsquo;ll get back to you soon.')
      setFormData({ name: '', email: '', subject: '', message: '' })
    } catch (error) {
      setStatus('error')
      setMessage('Failed to send message. Please try again.')
    }
  }

  return (
    <motion.div
      className="card max-w-2xl mx-auto"
      variants={ANIMATION_VARIANTS.slideUp}
      initial="hidden"
      animate="visible"
      transition={TRANSITIONS.default}
    >
      <div className="text-center mb-8">
        <h2 className="text-3xl font-heading font-bold text-savage-white mb-4">
          Get In <span className="text-gradient">Touch</span>
        </h2>
        <p className="text-savage-white/80">
          Have a question, collaboration idea, or just want to say hello? 
          We&rsquo;d love to hear from you.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name and Email */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="relative">
            <input
              type="text"
              name="name"
              placeholder="Your Name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-4 py-3 pl-12 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
              disabled={status === 'loading'}
              required
            />
            <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-savage-white/60" />
          </div>

          <div className="relative">
            <input
              type="email"
              name="email"
              placeholder="Your Email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full px-4 py-3 pl-12 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
              disabled={status === 'loading'}
              required
            />
            <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-savage-white/60" />
          </div>
        </div>

        {/* Subject */}
        <div>
          <select
            name="subject"
            value={formData.subject}
            onChange={handleInputChange}
            className="w-full px-4 py-3 glass rounded-lg text-savage-white focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
            disabled={status === 'loading'}
            required
          >
            <option value="">Select a subject</option>
            <option value="general">General Inquiry</option>
            <option value="collaboration">Collaboration</option>
            <option value="booking">Booking Request</option>
            <option value="press">Press & Media</option>
            <option value="support">Technical Support</option>
            <option value="other">Other</option>
          </select>
        </div>

        {/* Message */}
        <div className="relative">
          <textarea
            name="message"
            placeholder="Your Message"
            value={formData.message}
            onChange={handleInputChange}
            rows={6}
            className="w-full px-4 py-3 pl-12 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0 resize-none"
            disabled={status === 'loading'}
            required
          />
          <MessageSquare className="absolute left-4 top-4 w-5 h-5 text-savage-white/60" />
        </div>

        {/* Submit Button */}
        <motion.button
          type="submit"
          disabled={status === 'loading' || status === 'success'}
          className="w-full btn btn-primary text-lg py-4 disabled:opacity-50 disabled:cursor-not-allowed"
          whileHover={status === 'idle' ? { scale: 1.02 } : {}}
          whileTap={status === 'idle' ? { scale: 0.98 } : {}}
        >
          {status === 'loading' ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="w-5 h-5 border-2 border-savage-black border-t-transparent rounded-full animate-spin" />
              <span>Sending...</span>
            </div>
          ) : status === 'success' ? (
            <div className="flex items-center justify-center space-x-2">
              <Check className="w-5 h-5" />
              <span>Message Sent!</span>
            </div>
          ) : (
            <div className="flex items-center justify-center space-x-2">
              <Send className="w-5 h-5" />
              <span>Send Message</span>
            </div>
          )}
        </motion.button>

        {/* Status Message */}
        {message && (
          <motion.div
            className={`p-4 rounded-lg flex items-center space-x-2 ${
              status === 'success' 
                ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                : 'bg-red-500/20 text-red-400 border border-red-500/30'
            }`}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={TRANSITIONS.fast}
          >
            {status === 'success' ? (
              <Check className="w-5 h-5 flex-shrink-0" />
            ) : (
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
            )}
            <span className="text-sm">{message}</span>
          </motion.div>
        )}
      </form>
    </motion.div>
  )
}
