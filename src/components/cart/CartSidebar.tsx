'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { X, Plus, Minus, ShoppingBag, Trash2 } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { useCartStore } from '@/store/cart'
import { formatPrice } from '@/lib/utils'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'

export default function CartSidebar() {
  const { 
    isOpen, 
    items, 
    subtotal, 
    shipping, 
    tax, 
    total, 
    toggleCart, 
    updateQuantity, 
    removeItem 
  } = useCartStore()

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(itemId)
    } else {
      updateQuantity(itemId, newQuantity)
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={toggleCart}
          />

          {/* Sidebar */}
          <motion.div
            className="fixed top-0 right-0 h-full w-full max-w-md bg-savage-dark border-l border-glass-border z-50 flex flex-col"
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={TRANSITIONS.default}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-glass-border">
              <h2 className="text-xl font-semibold text-savage-white">
                Your Cart ({items.length})
              </h2>
              <button
                onClick={toggleCart}
                className="p-2 hover:bg-glass-light rounded-lg transition-colors duration-300"
              >
                <X className="w-5 h-5 text-savage-white" />
              </button>
            </div>

            {/* Cart Items */}
            <div className="flex-1 overflow-y-auto">
              {items.length === 0 ? (
                <motion.div
                  className="flex flex-col items-center justify-center h-full p-6 text-center"
                  variants={ANIMATION_VARIANTS.fadeIn}
                  initial="hidden"
                  animate="visible"
                >
                  <ShoppingBag className="w-16 h-16 text-savage-white/30 mb-4" />
                  <h3 className="text-lg font-medium text-savage-white mb-2">
                    Your cart is empty
                  </h3>
                  <p className="text-savage-white/60 mb-6">
                    Add some items to get started
                  </p>
                  <Link
                    href="/shop"
                    onClick={toggleCart}
                    className="btn btn-primary"
                  >
                    Continue Shopping
                  </Link>
                </motion.div>
              ) : (
                <div className="p-6 space-y-4">
                  {items.map((item, index) => (
                    <motion.div
                      key={item.id}
                      className="flex items-center space-x-4 p-4 glass rounded-lg"
                      variants={ANIMATION_VARIANTS.slideRight}
                      initial="hidden"
                      animate="visible"
                      transition={{ ...TRANSITIONS.default, delay: index * 0.1 }}
                    >
                      {/* Product Image */}
                      <div className="relative w-16 h-16 rounded-lg overflow-hidden">
                        <Image
                          src={item.image}
                          alt={item.name}
                          fill
                          className="object-cover"
                        />
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-savage-white truncate">
                          {item.name}
                        </h4>
                        <div className="flex items-center space-x-2 text-xs text-savage-white/60">
                          {item.color && (
                            <span className="capitalize">{item.color}</span>
                          )}
                          {item.size && (
                            <span className="uppercase">{item.size}</span>
                          )}
                        </div>
                        <p className="text-sm font-semibold text-savage-yellow">
                          {formatPrice(item.price)}
                        </p>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          className="p-1 hover:bg-glass-light rounded transition-colors duration-300"
                        >
                          <Minus className="w-4 h-4 text-savage-white" />
                        </button>
                        <span className="w-8 text-center text-sm font-medium text-savage-white">
                          {item.quantity}
                        </span>
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          className="p-1 hover:bg-glass-light rounded transition-colors duration-300"
                        >
                          <Plus className="w-4 h-4 text-savage-white" />
                        </button>
                      </div>

                      {/* Remove Button */}
                      <button
                        onClick={() => removeItem(item.id)}
                        className="p-1 hover:bg-savage-red/20 rounded transition-colors duration-300"
                      >
                        <Trash2 className="w-4 h-4 text-savage-red" />
                      </button>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {items.length > 0 && (
              <motion.div
                className="border-t border-glass-border p-6 space-y-4"
                variants={ANIMATION_VARIANTS.slideUp}
                initial="hidden"
                animate="visible"
                transition={{ ...TRANSITIONS.default, delay: 0.2 }}
              >
                {/* Totals */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-savage-white/80">
                    <span>Subtotal</span>
                    <span>{formatPrice(subtotal)}</span>
                  </div>
                  <div className="flex justify-between text-sm text-savage-white/80">
                    <span>Shipping</span>
                    <span>{shipping > 0 ? formatPrice(shipping) : 'Free'}</span>
                  </div>
                  <div className="flex justify-between text-sm text-savage-white/80">
                    <span>Tax</span>
                    <span>{formatPrice(tax)}</span>
                  </div>
                  <div className="flex justify-between text-lg font-semibold text-savage-white border-t border-glass-border pt-2">
                    <span>Total</span>
                    <span>{formatPrice(total)}</span>
                  </div>
                </div>

                {/* Checkout Button */}
                <Link
                  href="/checkout"
                  onClick={toggleCart}
                  className="w-full btn btn-primary text-center"
                >
                  Proceed to Checkout
                </Link>

                {/* Continue Shopping */}
                <Link
                  href="/shop"
                  onClick={toggleCart}
                  className="w-full btn btn-secondary text-center"
                >
                  Continue Shopping
                </Link>
              </motion.div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}
