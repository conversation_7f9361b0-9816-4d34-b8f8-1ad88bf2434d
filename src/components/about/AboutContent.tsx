'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { Play, Award, Users, Music, Calendar, MapPin } from 'lucide-react'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'

const achievements = [
  { icon: Music, number: '50M+', label: 'Streams Worldwide' },
  { icon: Users, number: '500K+', label: 'Monthly Listeners' },
  { icon: Calendar, number: '100+', label: 'Live Performances' },
  { icon: Award, number: '15+', label: 'Awards & Recognition' },
]

const timeline = [
  {
    year: '2018',
    title: 'The Beginning',
    description: 'Discovered her passion for music and songwriting, beginning to write and produce original songs that reflect personal experiences.'
  },
  {
    year: '2019',
    title: 'First Performances',
    description: 'Started performing at local venues and open mic nights, sharing original songs that resonated with audiences.'
  },
  {
    year: '2020',
    title: 'Breakthrough',
    description: 'Released first album "Artistic Sessions" which gained significant attention and established a growing fanbase.'
  },
  {
    year: '2021',
    title: 'Live Performances',
    description: 'Began touring and performing at various venues and music festivals, connecting with audiences through live music.'
  },
  {
    year: '2022',
    title: 'Expanding Reach',
    description: 'Music reached international audiences, touching lives through inspiring concerts and music festivals.'
  },
  {
    year: '2023',
    title: 'Brand Expansion',
    description: 'Launched merchandise line and exclusive content to connect with fans and support the artistic journey.'
  },
  {
    year: '2024',
    title: 'Present Day',
    description: 'Continuing to create inspiring music, touching hearts and connecting with people through authentic artistic expression.'
  }
]

export default function AboutContent() {
  return (
    <div className="section-padding">
      <div className="container">
        {/* Hero Section */}
        <motion.div
          className="text-center mb-20"
          variants={ANIMATION_VARIANTS.slideUp}
          initial="hidden"
          animate="visible"
          transition={TRANSITIONS.default}
        >
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-6">
            About <span className="text-gradient">Ace Monet</span>
          </h1>
          <p className="text-xl text-savage-white/80 max-w-3xl mx-auto leading-relaxed">
            Creating inspiring music that touches hearts and souls, with an unwavering
            commitment to artistic expression and connecting with people through song.
          </p>
        </motion.div>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Artist Image */}
          <motion.div
            className="relative"
            variants={ANIMATION_VARIANTS.slideRight}
            initial="hidden"
            animate="visible"
            transition={{ ...TRANSITIONS.default, delay: 0.2 }}
          >
            <div className="relative aspect-square rounded-2xl overflow-hidden shadow-glass">
              <Image
                src="https://images.unsplash.com/photo-1571330735066-03aaa9429d89?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80"
                alt="Worship Leader Ministry"
                fill
                className="object-cover"
                priority
              />
              
              {/* Floating Elements */}
              <div className="absolute -top-6 -right-6 w-12 h-12 bg-savage-yellow rounded-full animate-float" />
              <div className="absolute -bottom-6 -left-6 w-8 h-8 bg-savage-red rounded-full animate-float delay-300" />
            </div>
          </motion.div>

          {/* Story */}
          <motion.div
            className="space-y-6"
            variants={ANIMATION_VARIANTS.slideLeft}
            initial="hidden"
            animate="visible"
            transition={{ ...TRANSITIONS.default, delay: 0.4 }}
          >
            <h2 className="text-3xl font-heading font-bold text-savage-white">
              The <span className="text-gradient">Journey</span>
            </h2>
            
            <div className="space-y-4 text-savage-white/80 leading-relaxed">
              <p>
                Ace Monet&rsquo;s musical journey emerged from a deep passion for creating authentic, inspiring music.
                What started as personal songwriting sessions has evolved into an artistic vision that touches hearts
                and connects with people across the world, spreading inspiration through the power of song.
              </p>

              <p>
                Drawing inspiration from life experiences, personal growth, and the beauty of human connection,
                Ace crafts music that speaks to both the heart and soul. Each song is a carefully
                constructed journey of emotion, hope, and authenticity designed to inspire and uplift listeners.
              </p>
              
              <p>
                Beyond the music, our ministry represents a community of believers who seek authentic 
                worship and spiritual growth. Through live ministry events, Christian merchandise, 
                and direct fellowship, our ministry experience extends far beyond the sanctuary.
              </p>
            </div>

            <motion.button
              className="btn btn-primary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Play className="w-5 h-5 mr-2" />
              Listen to Latest Track
            </motion.button>
          </motion.div>
        </div>

        {/* Achievements */}
        <motion.div
          className="mb-20"
          variants={ANIMATION_VARIANTS.stagger}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <motion.h2
            className="text-3xl font-heading font-bold text-center text-savage-white mb-12"
            variants={ANIMATION_VARIANTS.slideUp}
          >
            Ministry <span className="text-gradient">Impact</span>
          </motion.h2>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => {
              const Icon = achievement.icon
              return (
                <motion.div
                  key={achievement.label}
                  className="text-center card-hover"
                  variants={ANIMATION_VARIANTS.scale}
                  transition={{ ...TRANSITIONS.default, delay: index * 0.1 }}
                >
                  <div className="p-4 bg-gradient-primary rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <Icon className="w-8 h-8 text-savage-black" />
                  </div>
                  <div className="text-3xl font-heading font-bold text-savage-yellow mb-2">
                    {achievement.number}
                  </div>
                  <div className="text-savage-white/80">
                    {achievement.label}
                  </div>
                </motion.div>
              )
            })}
          </div>
        </motion.div>

        {/* Timeline */}
        <motion.div
          className="mb-20"
          variants={ANIMATION_VARIANTS.stagger}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <motion.h2
            className="text-3xl font-heading font-bold text-center text-savage-white mb-12"
            variants={ANIMATION_VARIANTS.slideUp}
          >
            Ministry <span className="text-gradient">Timeline</span>
          </motion.h2>
          
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Timeline Line */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-primary" />
              
              <div className="space-y-12">
                {timeline.map((event, index) => (
                  <motion.div
                    key={event.year}
                    className="relative flex items-start space-x-8"
                    variants={ANIMATION_VARIANTS.slideLeft}
                    transition={{ ...TRANSITIONS.default, delay: index * 0.1 }}
                  >
                    {/* Year Marker */}
                    <div className="relative z-10 w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center text-savage-black font-bold">
                      {event.year}
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 card">
                      <h3 className="text-xl font-semibold text-savage-white mb-2">
                        {event.title}
                      </h3>
                      <p className="text-savage-white/80">
                        {event.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Philosophy */}
        <motion.div
          className="text-center card max-w-4xl mx-auto"
          variants={ANIMATION_VARIANTS.slideUp}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          transition={TRANSITIONS.default}
        >
          <h2 className="text-3xl font-heading font-bold text-savage-white mb-6">
            Our <span className="text-gradient">Mission</span>
          </h2>
          
          <blockquote className="text-xl text-savage-white/90 italic leading-relaxed mb-8">
            &ldquo;Music is not just sound—it&rsquo;s worship, testimony, and connection to the Almighty. Every note, every melody,
            every silence is crafted to create moments that glorify God and touch hearts for eternity.&rdquo;
          </blockquote>
          
          <div className="text-savage-yellow font-semibold">
            — Worship Leader Ministry
          </div>
        </motion.div>
      </div>
    </div>
  )
}
