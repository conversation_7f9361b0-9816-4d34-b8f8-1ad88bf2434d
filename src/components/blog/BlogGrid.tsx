'use client'

import { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import { Search, Filter, Calendar, User } from 'lucide-react'
import { BlogPost } from '@/types'
import BlogCard from './BlogCard'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'
import { debounce, sortBy as sortArray } from '@/lib/utils'

interface BlogGridProps {
  posts: BlogPost[]
  title?: string
  showSearch?: boolean
  showFilters?: boolean
}

type SortOption = 'newest' | 'oldest' | 'title'

export default function BlogGrid({ 
  posts, 
  title = 'Blog',
  showSearch = true,
  showFilters = true
}: BlogGridProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [sortBy, setSortBy] = useState<SortOption>('newest')
  
  // Debounced search
  const debouncedSearch = useMemo(
    () => debounce((query: string) => setSearchQuery(query), 300),
    []
  )

  // Get unique categories
  const categories = useMemo(() => {
    const allCategories = posts.map(post => post.category)
    return ['all', ...Array.from(new Set(allCategories))]
  }, [posts])

  // Filter and sort posts
  const filteredPosts = useMemo(() => {
    let filtered = posts

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())) ||
        post.author.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(post => post.category === selectedCategory)
    }

    // Sort posts
    switch (sortBy) {
      case 'newest':
        filtered = sortArray(filtered, 'publishedAt', 'desc')
        break
      case 'oldest':
        filtered = sortArray(filtered, 'publishedAt', 'asc')
        break
      case 'title':
        filtered = sortArray(filtered, 'title', 'asc')
        break
      default:
        break
    }

    return filtered
  }, [posts, searchQuery, selectedCategory, sortBy])

  // Separate featured and regular posts
  const featuredPosts = filteredPosts.filter(post => post.featured)
  const regularPosts = filteredPosts.filter(post => !post.featured)

  return (
    <div className="space-y-12">
      {/* Header */}
      <motion.div
        className="text-center"
        variants={ANIMATION_VARIANTS.slideUp}
        initial="hidden"
        animate="visible"
        transition={TRANSITIONS.default}
      >
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-4">
          {title} <span className="text-gradient">Stories</span>
        </h1>
        <p className="text-xl text-savage-white/80 max-w-2xl mx-auto">
          Behind the scenes, creative insights, and the latest news from the SAVAGE universe
        </p>
      </motion.div>

      {/* Search and Filters */}
      {(showSearch || showFilters) && (
        <motion.div
          className="space-y-6"
          variants={ANIMATION_VARIANTS.slideUp}
          initial="hidden"
          animate="visible"
          transition={{ ...TRANSITIONS.default, delay: 0.1 }}
        >
          {/* Search Bar */}
          {showSearch && (
            <div className="max-w-md mx-auto">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search articles..."
                  onChange={(e) => debouncedSearch(e.target.value)}
                  className="w-full px-4 py-3 pl-12 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                />
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-savage-white/60" />
              </div>
            </div>
          )}

          {/* Filters */}
          {showFilters && (
            <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 glass rounded-lg text-savage-white focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </option>
                ))}
              </select>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortOption)}
                className="px-4 py-2 glass rounded-lg text-savage-white focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="title">Title A-Z</option>
              </select>

              <span className="text-sm text-savage-white/60">
                {filteredPosts.length} articles
              </span>
            </div>
          )}
        </motion.div>
      )}

      {/* Featured Posts */}
      {featuredPosts.length > 0 && (
        <motion.section
          variants={ANIMATION_VARIANTS.stagger}
          initial="hidden"
          animate="visible"
        >
          <motion.h2
            className="text-2xl font-heading font-bold text-savage-white mb-8"
            variants={ANIMATION_VARIANTS.slideUp}
          >
            Featured Articles
          </motion.h2>
          
          <div className="space-y-8">
            {featuredPosts.map((post, index) => (
              <BlogCard
                key={post.id}
                post={post}
                index={index}
                featured={true}
              />
            ))}
          </div>
        </motion.section>
      )}

      {/* Regular Posts */}
      {regularPosts.length > 0 && (
        <motion.section
          variants={ANIMATION_VARIANTS.stagger}
          initial="hidden"
          animate="visible"
        >
          {featuredPosts.length > 0 && (
            <motion.h2
              className="text-2xl font-heading font-bold text-savage-white mb-8"
              variants={ANIMATION_VARIANTS.slideUp}
            >
              Latest Articles
            </motion.h2>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularPosts.map((post, index) => (
              <BlogCard
                key={post.id}
                post={post}
                index={index}
                featured={false}
              />
            ))}
          </div>
        </motion.section>
      )}

      {/* No Results */}
      {filteredPosts.length === 0 && (
        <motion.div
          className="text-center py-16"
          variants={ANIMATION_VARIANTS.fadeIn}
          initial="hidden"
          animate="visible"
        >
          <div className="text-6xl mb-4">📝</div>
          <h3 className="text-xl font-semibold text-savage-white mb-2">
            No articles found
          </h3>
          <p className="text-savage-white/60">
            Try adjusting your search or filter criteria
          </p>
        </motion.div>
      )}

      {/* Newsletter CTA */}
      <motion.div
        className="card text-center"
        variants={ANIMATION_VARIANTS.slideUp}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        transition={{ ...TRANSITIONS.default, delay: 0.2 }}
      >
        <h3 className="text-2xl font-heading font-bold text-savage-white mb-4">
          Stay Updated
        </h3>
        <p className="text-savage-white/80 mb-6">
          Subscribe to our newsletter to get the latest articles and updates delivered to your inbox.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
          <input
            type="email"
            placeholder="Your email address"
            className="flex-1 px-4 py-3 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
          />
          <button className="btn btn-primary">
            Subscribe
          </button>
        </div>
      </motion.div>
    </div>
  )
}
