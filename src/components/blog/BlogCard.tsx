'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, User, ArrowRight, Clock } from 'lucide-react'
import { BlogPost } from '@/types'
import { formatDate, truncateText } from '@/lib/utils'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'

interface BlogCardProps {
  post: BlogPost
  index?: number
  featured?: boolean
}

export default function BlogCard({ post, index = 0, featured = false }: BlogCardProps) {
  const readingTime = Math.ceil(post.content.length / 1000) // Rough estimate

  if (featured) {
    return (
      <motion.article
        className="card-hover overflow-hidden lg:grid lg:grid-cols-2 lg:gap-8"
        variants={ANIMATION_VARIANTS.slideUp}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        transition={{ ...TRANSITIONS.default, delay: index * 0.1 }}
      >
        <Link href={`/blog/${post.slug}`} className="group">
          {/* Featured Image */}
          <div className="relative aspect-video lg:aspect-square overflow-hidden rounded-lg mb-6 lg:mb-0">
            <Image
              src={post.featuredImage}
              alt={post.title}
              fill
              className="object-cover transition-transform duration-700 group-hover:scale-110"
            />
            
            {/* Featured Badge */}
            <div className="absolute top-4 left-4">
              <span className="px-3 py-1 bg-savage-yellow text-savage-black text-sm font-bold rounded">
                Featured
              </span>
            </div>

            {/* Category Badge */}
            <div className="absolute top-4 right-4">
              <span className="px-3 py-1 glass text-savage-white text-sm font-medium rounded">
                {post.category}
              </span>
            </div>

            {/* Overlay */}
            <div className="absolute inset-0 bg-gradient-overlay opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </div>

          {/* Content */}
          <div className="space-y-4">
            {/* Meta */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-savage-white/60">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(post.publishedAt)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <User className="w-4 h-4" />
                <span>{post.author}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4" />
                <span>{readingTime} min read</span>
              </div>
            </div>

            {/* Title */}
            <h2 className="text-2xl lg:text-3xl font-heading font-bold text-savage-white group-hover:text-savage-yellow transition-colors duration-300">
              {post.title}
            </h2>

            {/* Excerpt */}
            <p className="text-savage-white/80 leading-relaxed">
              {post.excerpt}
            </p>

            {/* Tags */}
            <div className="flex flex-wrap gap-2">
              {post.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag}
                  className="px-2 py-1 bg-savage-gray text-savage-white/80 text-xs rounded"
                >
                  #{tag}
                </span>
              ))}
            </div>

            {/* Read More */}
            <div className="flex items-center space-x-2 text-savage-yellow group-hover:text-savage-white transition-colors duration-300">
              <span className="font-medium">Read More</span>
              <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
            </div>
          </div>
        </Link>
      </motion.article>
    )
  }

  return (
    <motion.article
      className="card-hover overflow-hidden"
      variants={ANIMATION_VARIANTS.slideUp}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      transition={{ ...TRANSITIONS.default, delay: index * 0.1 }}
    >
      <Link href={`/blog/${post.slug}`} className="group">
        {/* Featured Image */}
        <div className="relative aspect-video overflow-hidden rounded-lg mb-4">
          <Image
            src={post.featuredImage}
            alt={post.title}
            fill
            className="object-cover transition-transform duration-700 group-hover:scale-110"
          />
          
          {/* Category Badge */}
          <div className="absolute top-3 right-3">
            <span className="px-2 py-1 glass text-savage-white text-xs font-medium rounded">
              {post.category}
            </span>
          </div>

          {/* Overlay */}
          <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        {/* Content */}
        <div className="space-y-3">
          {/* Meta */}
          <div className="flex items-center justify-between text-xs text-savage-white/60">
            <div className="flex items-center space-x-2">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(post.publishedAt)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="w-3 h-3" />
              <span>{readingTime} min</span>
            </div>
          </div>

          {/* Title */}
          <h3 className="text-lg font-semibold text-savage-white group-hover:text-savage-yellow transition-colors duration-300 line-clamp-2">
            {post.title}
          </h3>

          {/* Excerpt */}
          <p className="text-sm text-savage-white/70 line-clamp-3">
            {truncateText(post.excerpt, 120)}
          </p>

          {/* Tags */}
          <div className="flex flex-wrap gap-1">
            {post.tags.slice(0, 2).map((tag) => (
              <span
                key={tag}
                className="px-2 py-1 bg-savage-gray text-savage-white/70 text-xs rounded"
              >
                #{tag}
              </span>
            ))}
          </div>

          {/* Author and Read More */}
          <div className="flex items-center justify-between pt-2 border-t border-glass-border">
            <div className="flex items-center space-x-2 text-xs text-savage-white/60">
              <User className="w-3 h-3" />
              <span>{post.author}</span>
            </div>
            
            <div className="flex items-center space-x-1 text-xs text-savage-yellow group-hover:text-savage-white transition-colors duration-300">
              <span>Read</span>
              <ArrowRight className="w-3 h-3 group-hover:translate-x-0.5 transition-transform duration-300" />
            </div>
          </div>
        </div>
      </Link>
    </motion.article>
  )
}
