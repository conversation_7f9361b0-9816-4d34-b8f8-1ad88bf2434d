'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { Instagram, Twitter, Youtube, Music, Mail } from 'lucide-react'
import { NAVIGATION_ITEMS, SOCIAL_PLATFORMS } from '@/lib/constants'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'

const socialIcons = {
  instagram: Instagram,
  twitter: Twitter,
  youtube: Youtube,
  tiktok: Music,
  spotify: Music,
}

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-gradient-dark border-t border-glass-border">
      <div className="container section-padding">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
          {/* Brand Section */}
          <motion.div
            className="lg:col-span-1"
            variants={ANIMATION_VARIANTS.slideUp}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            transition={TRANSITIONS.default}
          >
            <Link
              href="/"
              className="text-3xl font-heading font-bold text-gradient mb-4 block hover:scale-105 transition-transform duration-300"
            >
              WORSHIP LEADER
            </Link>
            <p className="text-savage-white/80 mb-6 leading-relaxed">
              Spreading the Gospel through music since 2018.
              Experience worship that transforms hearts with inspiring songs,
              merchandise, and live ministry events.
            </p>
            <div className="flex space-x-4">
              {SOCIAL_PLATFORMS.map((platform) => {
                const Icon = socialIcons[platform.value as keyof typeof socialIcons]
                return (
                  <motion.a
                    key={platform.value}
                    href={platform.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-3 glass rounded-lg hover:bg-glass-light transition-all duration-300 group"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Icon className="w-5 h-5 text-savage-white group-hover:text-savage-yellow transition-colors duration-300" />
                  </motion.a>
                )
              })}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            transition={{ ...TRANSITIONS.default, delay: 0.1 }}
          >
            <h4 className="text-lg font-semibold text-savage-white mb-6">Quick Links</h4>
            <ul className="space-y-3">
              {NAVIGATION_ITEMS.map((item) => (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className="text-savage-white/80 hover:text-savage-yellow transition-colors duration-300 flex items-center group"
                  >
                    <span className="w-2 h-2 bg-savage-yellow rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Shop Categories */}
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            transition={{ ...TRANSITIONS.default, delay: 0.2 }}
          >
            <h4 className="text-lg font-semibold text-savage-white mb-6">Shop</h4>
            <ul className="space-y-3">
              {['Clothing', 'Music', 'Accessories', 'Bundles', 'Gift Cards'].map((category) => (
                <li key={category}>
                  <Link
                    href={`/shop?category=${category.toLowerCase()}`}
                    className="text-savage-white/80 hover:text-savage-yellow transition-colors duration-300 flex items-center group"
                  >
                    <span className="w-2 h-2 bg-savage-yellow rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    {category}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Newsletter */}
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            transition={{ ...TRANSITIONS.default, delay: 0.3 }}
          >
            <h4 className="text-lg font-semibold text-savage-white mb-6">Stay Updated</h4>
            <p className="text-savage-white/80 mb-4">
              Subscribe to get exclusive updates, new releases, and special offers.
            </p>
            <form className="space-y-3">
              <div className="relative">
                <input
                  type="email"
                  placeholder="Your email address"
                  className="w-full px-4 py-3 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                  required
                />
                <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-savage-white/60" />
              </div>
              <motion.button
                type="submit"
                className="w-full btn btn-primary"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Subscribe
              </motion.button>
            </form>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div
          className="border-t border-glass-border mt-12 pt-8 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0"
          variants={ANIMATION_VARIANTS.fadeIn}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          transition={{ ...TRANSITIONS.default, delay: 0.4 }}
        >
          <p className="text-savage-white/60 text-sm">
            &copy; {currentYear} SAVAGE. All Rights Reserved.
          </p>
          <div className="flex space-x-6">
            {['Privacy Policy', 'Terms of Service', 'Shipping & Returns'].map((link) => (
              <Link
                key={link}
                href={`/${link.toLowerCase().replace(/\s+/g, '-')}`}
                className="text-savage-white/60 hover:text-savage-yellow text-sm transition-colors duration-300"
              >
                {link}
              </Link>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-savage-yellow via-transparent to-savage-red" />
      </div>
    </footer>
  )
}
