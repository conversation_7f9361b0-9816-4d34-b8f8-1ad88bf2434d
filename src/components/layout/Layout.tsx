'use client'

import { ReactNode } from 'react'
import { motion } from 'framer-motion'
import Header from './Header'
import Footer from './Footer'
import CartSidebar from '@/components/cart/CartSidebar'
import MusicPlayer from '@/components/music/MusicPlayer'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'

interface LayoutProps {
  children: ReactNode
  className?: string
  showHeader?: boolean
  showFooter?: boolean
  showPlayer?: boolean
}

export default function Layout({ 
  children, 
  className = '',
  showHeader = true,
  showFooter = true,
  showPlayer = true
}: LayoutProps) {
  return (
    <div className="min-h-screen bg-savage-black text-savage-white overflow-x-hidden">
      {/* Header */}
      {showHeader && <Header />}

      {/* Main Content */}
      <motion.main
        className={`${showHeader ? 'pt-20' : ''} ${className}`}
        variants={ANIMATION_VARIANTS.fadeIn}
        initial="hidden"
        animate="visible"
        transition={TRANSITIONS.default}
      >
        {children}
      </motion.main>

      {/* Footer */}
      {showFooter && <Footer />}

      {/* Cart Sidebar */}
      <CartSidebar />

      {/* Music Player */}
      {showPlayer && <MusicPlayer />}

      {/* Background Effects */}
      <div className="fixed inset-0 pointer-events-none z-0">
        {/* Gradient Orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-savage-yellow/5 rounded-full blur-3xl animate-float" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-savage-red/5 rounded-full blur-3xl animate-float delay-300" />
        
        {/* Grid Pattern */}
        <div 
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 215, 0, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 215, 0, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}
        />
      </div>
    </div>
  )
}
