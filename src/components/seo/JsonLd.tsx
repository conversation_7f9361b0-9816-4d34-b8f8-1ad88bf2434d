interface JsonLdProps {
  data: Record<string, any>
}

export default function JsonLd({ data }: JsonLdProps) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  )
}

// Predefined structured data schemas
export const schemas = {
  organization: {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Ace Monet',
    description: 'Inspiring music artist creating authentic songs that touch hearts and connect with people.',
    url: process.env.SITE_URL || 'https://ace-monet.herokuapp.com',
    logo: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200',
    sameAs: [
      'https://www.facebook.com/acemonet',
      'https://www.instagram.com/ace_monet_music',
      'https://www.youtube.com/ace_monet_music',
      'https://open.spotify.com/artist/ace_monet',
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '******-123-4567',
      contactType: 'customer service',
      email: '<EMAIL>',
    },
  },

  musicGroup: {
    '@context': 'https://schema.org',
    '@type': 'MusicGroup',
    name: 'Ace Monet',
    genre: ['Pop', 'Indie', 'Alternative'],
    description: 'Inspiring music artist creating authentic songs that connect with people and touch hearts.',
    url: process.env.SITE_URL || 'https://ace-monet.herokuapp.com',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=630',
  },

  product: (product: any) => ({
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description,
    image: product.images,
    brand: {
      '@type': 'Brand',
      name: 'Worship Leader',
    },
    offers: {
      '@type': 'Offer',
      price: product.price,
      priceCurrency: 'USD',
      availability: product.stock > 0 ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
      seller: {
        '@type': 'Organization',
        name: 'Worship Leader Ministry',
      },
    },
    aggregateRating: product.rating ? {
      '@type': 'AggregateRating',
      ratingValue: product.rating,
      reviewCount: product.reviewCount || 1,
    } : undefined,
  }),

  musicAlbum: (album: any) => ({
    '@context': 'https://schema.org',
    '@type': 'MusicAlbum',
    name: album.name,
    description: album.description,
    image: album.artwork,
    byArtist: {
      '@type': 'MusicGroup',
      name: 'Worship Leader',
    },
    datePublished: album.releaseDate,
    genre: album.genre,
    numTracks: album.trackCount,
  }),

  event: (event: any) => ({
    '@context': 'https://schema.org',
    '@type': 'MusicEvent',
    name: event.title,
    description: `Worship concert featuring Worship Leader at ${event.venue}`,
    startDate: event.date,
    location: {
      '@type': 'Place',
      name: event.venue,
      address: {
        '@type': 'PostalAddress',
        addressLocality: event.city,
        addressRegion: event.state,
        addressCountry: event.country,
      },
    },
    performer: {
      '@type': 'MusicGroup',
      name: 'Worship Leader',
    },
    offers: event.price ? {
      '@type': 'Offer',
      price: event.price,
      priceCurrency: 'USD',
      availability: event.soldOut ? 'https://schema.org/SoldOut' : 'https://schema.org/InStock',
    } : undefined,
  }),

  blogPost: (post: any) => ({
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: post.title,
    description: post.excerpt,
    image: post.featuredImage,
    author: {
      '@type': 'Person',
      name: post.author,
    },
    publisher: {
      '@type': 'Organization',
      name: 'Worship Leader Ministry',
      logo: {
        '@type': 'ImageObject',
        url: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200',
      },
    },
    datePublished: post.publishedAt,
    dateModified: post.updatedAt,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${process.env.SITE_URL}/blog/${post.slug}`,
    },
  }),
}
