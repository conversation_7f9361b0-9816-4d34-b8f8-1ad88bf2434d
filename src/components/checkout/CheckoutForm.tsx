'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { loadStripe } from '@stripe/stripe-js'
import { CreditCard, Lock, User, MapPin, Mail, Phone } from 'lucide-react'
import { useCartStore } from '@/store/cart'
import { formatPrice } from '@/lib/utils'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)

interface CheckoutFormData {
  email: string
  firstName: string
  lastName: string
  address: string
  city: string
  state: string
  zipCode: string
  country: string
  phone: string
  cardNumber: string
  expiryDate: string
  cvv: string
  cardName: string
}

export default function CheckoutForm() {
  const { items, subtotal, shipping, tax, total, clearCart } = useCartStore()
  const [formData, setFormData] = useState<CheckoutFormData>({
    email: '',
    firstName: '',
    lastName: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'US',
    phone: '',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardName: '',
  })
  const [isProcessing, setIsProcessing] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsProcessing(true)

    try {
      // Create checkout session
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items,
          customerEmail: formData.email,
          shippingAddress: {
            name: `${formData.firstName} ${formData.lastName}`,
            address: formData.address,
            city: formData.city,
            state: formData.state,
            zipCode: formData.zipCode,
            country: formData.country,
          },
          billingAddress: {
            name: `${formData.firstName} ${formData.lastName}`,
            address: formData.address,
            city: formData.city,
            state: formData.state,
            zipCode: formData.zipCode,
            country: formData.country,
          },
        }),
      })

      const { clientSecret, orderNumber } = await response.json()

      if (!clientSecret) {
        throw new Error('Failed to create payment intent')
      }

      // For demo purposes, simulate successful payment
      // In production, you'd use Stripe Elements for card input
      clearCart()
      alert(`Order ${orderNumber} placed successfully! Payment processing...`)

    } catch (error) {
      console.error('Payment error:', error)
      alert('Payment failed. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  if (items.length === 0) {
    return (
      <div className="text-center py-16">
        <h2 className="text-2xl font-semibold text-savage-white mb-4">
          Your cart is empty
        </h2>
        <p className="text-savage-white/60 mb-8">
          Add some items to your cart before checking out
        </p>
        <a href="/shop" className="btn btn-primary">
          Continue Shopping
        </a>
      </div>
    )
  }

  return (
    <div className="grid lg:grid-cols-2 gap-12">
      {/* Checkout Form */}
      <motion.div
        className="space-y-8"
        variants={ANIMATION_VARIANTS.slideUp}
        initial="hidden"
        animate="visible"
        transition={TRANSITIONS.default}
      >
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Contact Information */}
          <div className="card">
            <h3 className="text-xl font-semibold text-savage-white mb-6 flex items-center">
              <Mail className="w-5 h-5 mr-2 text-savage-yellow" />
              Contact Information
            </h3>
            <div className="space-y-4">
              <input
                type="email"
                name="email"
                placeholder="Email address"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full px-4 py-3 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                required
              />
            </div>
          </div>

          {/* Shipping Address */}
          <div className="card">
            <h3 className="text-xl font-semibold text-savage-white mb-6 flex items-center">
              <MapPin className="w-5 h-5 mr-2 text-savage-yellow" />
              Shipping Address
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <input
                type="text"
                name="firstName"
                placeholder="First name"
                value={formData.firstName}
                onChange={handleInputChange}
                className="px-4 py-3 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                required
              />
              <input
                type="text"
                name="lastName"
                placeholder="Last name"
                value={formData.lastName}
                onChange={handleInputChange}
                className="px-4 py-3 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                required
              />
              <input
                type="text"
                name="address"
                placeholder="Address"
                value={formData.address}
                onChange={handleInputChange}
                className="md:col-span-2 px-4 py-3 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                required
              />
              <input
                type="text"
                name="city"
                placeholder="City"
                value={formData.city}
                onChange={handleInputChange}
                className="px-4 py-3 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                required
              />
              <input
                type="text"
                name="state"
                placeholder="State"
                value={formData.state}
                onChange={handleInputChange}
                className="px-4 py-3 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                required
              />
              <input
                type="text"
                name="zipCode"
                placeholder="ZIP Code"
                value={formData.zipCode}
                onChange={handleInputChange}
                className="px-4 py-3 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                required
              />
              <select
                name="country"
                value={formData.country}
                onChange={handleInputChange}
                className="px-4 py-3 glass rounded-lg text-savage-white focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                required
              >
                <option value="US">United States</option>
                <option value="CA">Canada</option>
                <option value="UK">United Kingdom</option>
                <option value="AU">Australia</option>
              </select>
            </div>
          </div>

          {/* Payment Information */}
          <div className="card">
            <h3 className="text-xl font-semibold text-savage-white mb-6 flex items-center">
              <CreditCard className="w-5 h-5 mr-2 text-savage-yellow" />
              Payment Information
            </h3>
            <div className="space-y-4">
              <input
                type="text"
                name="cardNumber"
                placeholder="Card number"
                value={formData.cardNumber}
                onChange={handleInputChange}
                className="w-full px-4 py-3 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                required
              />
              <div className="grid grid-cols-2 gap-4">
                <input
                  type="text"
                  name="expiryDate"
                  placeholder="MM/YY"
                  value={formData.expiryDate}
                  onChange={handleInputChange}
                  className="px-4 py-3 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                  required
                />
                <input
                  type="text"
                  name="cvv"
                  placeholder="CVV"
                  value={formData.cvv}
                  onChange={handleInputChange}
                  className="px-4 py-3 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                  required
                />
              </div>
              <input
                type="text"
                name="cardName"
                placeholder="Name on card"
                value={formData.cardName}
                onChange={handleInputChange}
                className="w-full px-4 py-3 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
                required
              />
            </div>
          </div>

          {/* Submit Button */}
          <motion.button
            type="submit"
            disabled={isProcessing}
            className="w-full btn btn-primary text-lg py-4 disabled:opacity-50 disabled:cursor-not-allowed"
            whileHover={!isProcessing ? { scale: 1.02 } : {}}
            whileTap={!isProcessing ? { scale: 0.98 } : {}}
          >
            {isProcessing ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-5 h-5 border-2 border-savage-black border-t-transparent rounded-full animate-spin" />
                <span>Processing...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-2">
                <Lock className="w-5 h-5" />
                <span>Complete Order - {formatPrice(total)}</span>
              </div>
            )}
          </motion.button>
        </form>
      </motion.div>

      {/* Order Summary */}
      <motion.div
        className="space-y-6"
        variants={ANIMATION_VARIANTS.slideUp}
        initial="hidden"
        animate="visible"
        transition={{ ...TRANSITIONS.default, delay: 0.2 }}
      >
        <div className="card">
          <h3 className="text-xl font-semibold text-savage-white mb-6">
            Order Summary
          </h3>
          
          {/* Items */}
          <div className="space-y-4 mb-6">
            {items.map((item) => (
              <div key={item.id} className="flex items-center space-x-4">
                <div className="relative w-16 h-16 rounded-lg overflow-hidden">
                  <Image
                    src={item.image}
                    alt={item.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-savage-white truncate">
                    {item.name}
                  </h4>
                  <div className="text-xs text-savage-white/60">
                    Qty: {item.quantity}
                    {item.color && ` • ${item.color}`}
                    {item.size && ` • ${item.size}`}
                  </div>
                </div>
                <div className="text-sm font-semibold text-savage-yellow">
                  {formatPrice(item.price * item.quantity)}
                </div>
              </div>
            ))}
          </div>

          {/* Totals */}
          <div className="border-t border-glass-border pt-4 space-y-2">
            <div className="flex justify-between text-savage-white/80">
              <span>Subtotal</span>
              <span>{formatPrice(subtotal)}</span>
            </div>
            <div className="flex justify-between text-savage-white/80">
              <span>Shipping</span>
              <span>{shipping > 0 ? formatPrice(shipping) : 'Free'}</span>
            </div>
            <div className="flex justify-between text-savage-white/80">
              <span>Tax</span>
              <span>{formatPrice(tax)}</span>
            </div>
            <div className="flex justify-between text-lg font-semibold text-savage-white border-t border-glass-border pt-2">
              <span>Total</span>
              <span>{formatPrice(total)}</span>
            </div>
          </div>
        </div>

        {/* Security Notice */}
        <div className="card">
          <div className="flex items-center space-x-3 text-savage-white/80">
            <Lock className="w-5 h-5 text-savage-yellow" />
            <div>
              <p className="text-sm font-medium">Secure Checkout</p>
              <p className="text-xs">Your payment information is encrypted and secure</p>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
