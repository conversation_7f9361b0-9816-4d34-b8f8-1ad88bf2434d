'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Package, 
  ShoppingCart, 
  Users, 
  Mail, 
  FileText, 
  Music, 
  Calendar,
  BarChart3,
  Settings
} from 'lucide-react'
import Layout from '@/components/layout/Layout'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'

const adminSections = [
  {
    id: 'overview',
    name: 'Overview',
    icon: BarChart3,
    description: 'Dashboard overview and analytics'
  },
  {
    id: 'products',
    name: 'Products',
    icon: Package,
    description: 'Manage store products and inventory'
  },
  {
    id: 'orders',
    name: 'Orders',
    icon: ShoppingCart,
    description: 'View and manage customer orders'
  },
  {
    id: 'users',
    name: 'Users',
    icon: Users,
    description: 'Manage user accounts and permissions'
  },
  {
    id: 'content',
    name: 'Content',
    icon: FileText,
    description: 'Manage blog posts and pages'
  },
  {
    id: 'music',
    name: 'Music',
    icon: Music,
    description: 'Manage tracks and albums'
  },
  {
    id: 'events',
    name: 'Events',
    icon: Calendar,
    description: 'Manage shows and ministry events'
  },
  {
    id: 'contact',
    name: 'Contact',
    icon: Mail,
    description: 'View contact form submissions'
  },
  {
    id: 'settings',
    name: 'Settings',
    icon: Settings,
    description: 'Site settings and configuration'
  }
]

export default function AdminDashboard() {
  const [activeSection, setActiveSection] = useState('overview')

  return (
    <Layout>
      <div className="section-padding">
        <div className="container">
          {/* Header */}
          <motion.div
            className="text-center mb-12"
            variants={ANIMATION_VARIANTS.slideUp}
            initial="hidden"
            animate="visible"
            transition={TRANSITIONS.default}
          >
            <h1 className="text-4xl font-heading font-bold text-savage-white mb-4">
              Admin <span className="text-gradient">Dashboard</span>
            </h1>
            <p className="text-xl text-savage-white/80">
              Manage your ministry website and e-commerce platform
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-4 gap-8">
            {/* Sidebar Navigation */}
            <motion.div
              className="lg:col-span-1"
              variants={ANIMATION_VARIANTS.slideRight}
              initial="hidden"
              animate="visible"
              transition={{ ...TRANSITIONS.default, delay: 0.2 }}
            >
              <div className="card">
                <h2 className="text-xl font-semibold text-savage-white mb-6">
                  Admin Sections
                </h2>
                <nav className="space-y-2">
                  {adminSections.map((section) => {
                    const Icon = section.icon
                    return (
                      <button
                        key={section.id}
                        onClick={() => setActiveSection(section.id)}
                        className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-300 text-left ${
                          activeSection === section.id
                            ? 'bg-gradient-primary text-savage-black'
                            : 'text-savage-white/80 hover:bg-glass-light hover:text-savage-white'
                        }`}
                      >
                        <Icon className="w-5 h-5" />
                        <span className="font-medium">{section.name}</span>
                      </button>
                    )
                  })}
                </nav>
              </div>
            </motion.div>

            {/* Main Content */}
            <motion.div
              className="lg:col-span-3"
              variants={ANIMATION_VARIANTS.slideLeft}
              initial="hidden"
              animate="visible"
              transition={{ ...TRANSITIONS.default, delay: 0.4 }}
            >
              <div className="card">
                {activeSection === 'overview' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-savage-white mb-6">
                      Dashboard Overview
                    </h2>
                    
                    {/* Stats Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                      {[
                        { label: 'Total Orders', value: '156', change: '+12%' },
                        { label: 'Revenue', value: '$3,247', change: '+8%' },
                        { label: 'Products', value: '24', change: '+2' },
                        { label: 'Subscribers', value: '1,234', change: '+45' },
                      ].map((stat, index) => (
                        <div key={stat.label} className="glass p-4 rounded-lg">
                          <p className="text-savage-white/60 text-sm">{stat.label}</p>
                          <p className="text-2xl font-bold text-savage-white">{stat.value}</p>
                          <p className="text-green-400 text-sm">{stat.change}</p>
                        </div>
                      ))}
                    </div>

                    {/* Recent Activity */}
                    <div>
                      <h3 className="text-lg font-semibold text-savage-white mb-4">
                        Recent Activity
                      </h3>
                      <div className="space-y-3">
                        {[
                          'New order #WL-1234567890 received',
                          'Product "Faith Over Fear T-Shirt" updated',
                          'New contact form submission from John Doe',
                          'Newsletter subscriber added: <EMAIL>',
                        ].map((activity, index) => (
                          <div key={index} className="glass p-3 rounded-lg">
                            <p className="text-savage-white/80">{activity}</p>
                            <p className="text-savage-white/60 text-sm">2 hours ago</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {activeSection === 'products' && (
                  <div>
                    <div className="flex justify-between items-center mb-6">
                      <h2 className="text-2xl font-semibold text-savage-white">
                        Product Management
                      </h2>
                      <button className="btn btn-primary">
                        Add New Product
                      </button>
                    </div>
                    <p className="text-savage-white/80">
                      Product management interface would go here. This would include:
                    </p>
                    <ul className="list-disc list-inside text-savage-white/80 mt-4 space-y-2">
                      <li>Product listing with search and filters</li>
                      <li>Add/edit product forms</li>
                      <li>Image upload and management</li>
                      <li>Inventory tracking</li>
                      <li>Category management</li>
                    </ul>
                  </div>
                )}

                {activeSection === 'orders' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-savage-white mb-6">
                      Order Management
                    </h2>
                    <p className="text-savage-white/80">
                      Order management interface would include:
                    </p>
                    <ul className="list-disc list-inside text-savage-white/80 mt-4 space-y-2">
                      <li>Order listing with status filters</li>
                      <li>Order details and customer information</li>
                      <li>Status updates and tracking</li>
                      <li>Refund and cancellation handling</li>
                      <li>Export functionality for accounting</li>
                    </ul>
                  </div>
                )}

                {activeSection === 'users' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-savage-white mb-6">
                      User Management
                    </h2>
                    <p className="text-savage-white/80">
                      User management features would include:
                    </p>
                    <ul className="list-disc list-inside text-savage-white/80 mt-4 space-y-2">
                      <li>User listing and search</li>
                      <li>Role management (Admin, User)</li>
                      <li>Account status controls</li>
                      <li>Order history per user</li>
                      <li>Communication tools</li>
                    </ul>
                  </div>
                )}

                {activeSection === 'content' && (
                  <div>
                    <div className="flex justify-between items-center mb-6">
                      <h2 className="text-2xl font-semibold text-savage-white">
                        Content Management
                      </h2>
                      <button className="btn btn-primary">
                        New Blog Post
                      </button>
                    </div>
                    <p className="text-savage-white/80">
                      Content management system would include:
                    </p>
                    <ul className="list-disc list-inside text-savage-white/80 mt-4 space-y-2">
                      <li>Blog post creation and editing</li>
                      <li>Rich text editor with media support</li>
                      <li>SEO optimization tools</li>
                      <li>Publishing and scheduling</li>
                      <li>Category and tag management</li>
                    </ul>
                  </div>
                )}

                {/* Add other sections as needed */}
                {!['overview', 'products', 'orders', 'users', 'content'].includes(activeSection) && (
                  <div>
                    <h2 className="text-2xl font-semibold text-savage-white mb-6">
                      {adminSections.find(s => s.id === activeSection)?.name}
                    </h2>
                    <p className="text-savage-white/80">
                      {adminSections.find(s => s.id === activeSection)?.description}
                    </p>
                    <p className="text-savage-white/60 mt-4">
                      This section is under development and will be available in future updates.
                    </p>
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
