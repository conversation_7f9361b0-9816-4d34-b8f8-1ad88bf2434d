'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Save, 
  X, 
  Upload, 
  Eye, 
  Calendar,
  Tag,
  Image as ImageIcon,
  Music,
  Package,
  FileText
} from 'lucide-react'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'

interface ContentEditorProps {
  contentType: 'blog' | 'track' | 'product' | 'event'
  item?: any
  onSave: (data: any) => void
  onCancel: () => void
}

const contentTypeConfig = {
  blog: {
    title: 'Blog Post',
    icon: FileText,
    fields: {
      title: { type: 'text', label: 'Title', required: true },
      slug: { type: 'text', label: 'URL Slug', required: true },
      excerpt: { type: 'textarea', label: 'Excerpt', required: true },
      content: { type: 'textarea', label: 'Content', required: true, rows: 10 },
      author: { type: 'text', label: 'Author', required: true },
      featuredImage: { type: 'url', label: 'Featured Image URL', required: false },
      category: { type: 'select', label: 'Category', required: true, options: ['Personal', 'Music', 'Behind the Scenes', 'News'] },
      tags: { type: 'tags', label: 'Tags', required: false },
      featured: { type: 'checkbox', label: 'Featured Post', required: false },
      published: { type: 'checkbox', label: 'Published', required: false }
    }
  },
  track: {
    title: 'Music Track',
    icon: Music,
    fields: {
      title: { type: 'text', label: 'Track Title', required: true },
      artist: { type: 'text', label: 'Artist', required: true },
      album: { type: 'text', label: 'Album', required: false },
      duration: { type: 'number', label: 'Duration (seconds)', required: true },
      artwork: { type: 'url', label: 'Artwork URL', required: false },
      genre: { type: 'tags', label: 'Genres', required: false },
      featured: { type: 'checkbox', label: 'Featured Track', required: false },
      releaseDate: { type: 'date', label: 'Release Date', required: true }
    }
  },
  product: {
    title: 'Product',
    icon: Package,
    fields: {
      name: { type: 'text', label: 'Product Name', required: true },
      slug: { type: 'text', label: 'URL Slug', required: false },
      description: { type: 'textarea', label: 'Description', required: true },
      price: { type: 'number', label: 'Price ($)', required: true, step: 0.01 },
      category: { type: 'select', label: 'Category', required: true, options: ['clothing', 'music', 'accessories', 'bundles'] },
      images: { type: 'tags', label: 'Image URLs', required: false },
      tags: { type: 'tags', label: 'Tags', required: false },
      featured: { type: 'checkbox', label: 'Featured Product', required: false },
      stock: { type: 'number', label: 'Stock Count', required: false }
    }
  },
  event: {
    title: 'Event',
    icon: Calendar,
    fields: {
      title: { type: 'text', label: 'Event Title', required: true },
      venue: { type: 'text', label: 'Venue', required: true },
      city: { type: 'text', label: 'City', required: true },
      state: { type: 'text', label: 'State/Province', required: false },
      country: { type: 'text', label: 'Country', required: false },
      date: { type: 'datetime-local', label: 'Event Date & Time', required: true },
      ticketUrl: { type: 'url', label: 'Ticket URL', required: false },
      featured: { type: 'checkbox', label: 'Featured Event', required: false }
    }
  }
}

export default function ContentEditor({ 
  contentType, 
  item, 
  onSave, 
  onCancel 
}: ContentEditorProps) {
  const [formData, setFormData] = useState<any>({})
  const [loading, setSaving] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const config = contentTypeConfig[contentType]
  const Icon = config.icon
  const isEditing = !!item

  useEffect(() => {
    if (item) {
      setFormData(item)
    } else {
      // Initialize with default values
      const defaultData: any = {}
      Object.entries(config.fields).forEach(([key, field]) => {
        if (field.type === 'checkbox') {
          defaultData[key] = false
        } else if (field.type === 'tags') {
          defaultData[key] = []
        } else {
          defaultData[key] = ''
        }
      })
      setFormData(defaultData)
    }
  }, [item, config.fields])

  const handleInputChange = (key: string, value: any) => {
    setFormData((prev: any) => ({ ...prev, [key]: value }))
    if (errors[key]) {
      setErrors(prev => ({ ...prev, [key]: '' }))
    }
  }

  const handleTagsChange = (key: string, value: string) => {
    const tags = value.split(',').map(tag => tag.trim()).filter(tag => tag)
    handleInputChange(key, tags)
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    Object.entries(config.fields).forEach(([key, field]) => {
      if (field.required && (!formData[key] || formData[key] === '')) {
        newErrors[key] = `${field.label} is required`
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setSaving(true)
    try {
      await onSave(formData)
    } catch (error) {
      console.error('Error saving content:', error)
    } finally {
      setSaving(false)
    }
  }

  const renderField = (key: string, field: any) => {
    const value = formData[key] || ''
    const error = errors[key]

    switch (field.type) {
      case 'text':
      case 'url':
      case 'number':
      case 'date':
      case 'datetime-local':
        return (
          <div key={key} className="space-y-2">
            <label className="block text-sm font-medium text-savage-white">
              {field.label} {field.required && <span className="text-red-400">*</span>}
            </label>
            <input
              type={field.type}
              value={value}
              onChange={(e) => handleInputChange(key, e.target.value)}
              step={(field as any).step}
              className={`w-full px-4 py-2 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0 ${
                error ? 'ring-2 ring-red-400' : ''
              }`}
              placeholder={field.label}
            />
            {error && <p className="text-red-400 text-sm">{error}</p>}
          </div>
        )

      case 'textarea':
        return (
          <div key={key} className="space-y-2">
            <label className="block text-sm font-medium text-savage-white">
              {field.label} {field.required && <span className="text-red-400">*</span>}
            </label>
            <textarea
              value={value}
              onChange={(e) => handleInputChange(key, e.target.value)}
              rows={(field as any).rows || 4}
              className={`w-full px-4 py-2 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0 resize-none ${
                error ? 'ring-2 ring-red-400' : ''
              }`}
              placeholder={field.label}
            />
            {error && <p className="text-red-400 text-sm">{error}</p>}
          </div>
        )

      case 'select':
        return (
          <div key={key} className="space-y-2">
            <label className="block text-sm font-medium text-savage-white">
              {field.label} {field.required && <span className="text-red-400">*</span>}
            </label>
            <select
              value={value}
              onChange={(e) => handleInputChange(key, e.target.value)}
              className={`w-full px-4 py-2 glass rounded-lg text-savage-white focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0 ${
                error ? 'ring-2 ring-red-400' : ''
              }`}
            >
              <option value="">Select {field.label}</option>
              {(field as any).options?.map((option: string) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
            {error && <p className="text-red-400 text-sm">{error}</p>}
          </div>
        )

      case 'tags':
        return (
          <div key={key} className="space-y-2">
            <label className="block text-sm font-medium text-savage-white">
              {field.label} {field.required && <span className="text-red-400">*</span>}
            </label>
            <input
              type="text"
              value={Array.isArray(value) ? value.join(', ') : ''}
              onChange={(e) => handleTagsChange(key, e.target.value)}
              className={`w-full px-4 py-2 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0 ${
                error ? 'ring-2 ring-red-400' : ''
              }`}
              placeholder="Enter tags separated by commas"
            />
            {error && <p className="text-red-400 text-sm">{error}</p>}
          </div>
        )

      case 'checkbox':
        return (
          <div key={key} className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={value || false}
              onChange={(e) => handleInputChange(key, e.target.checked)}
              className="rounded border-savage-white/20 bg-transparent text-savage-yellow focus:ring-savage-yellow"
            />
            <label className="text-sm font-medium text-savage-white">
              {field.label}
            </label>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <motion.div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      variants={ANIMATION_VARIANTS.fadeIn}
      initial="hidden"
      animate="visible"
      exit="hidden"
    >
      <motion.div
        className="bg-savage-black border border-glass-border rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden"
        variants={ANIMATION_VARIANTS.scale}
        initial="hidden"
        animate="visible"
        exit="hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-glass-border">
          <div className="flex items-center space-x-3">
            <Icon className="w-6 h-6 text-savage-yellow" />
            <h2 className="text-xl font-semibold text-savage-white">
              {isEditing ? 'Edit' : 'Create'} {config.title}
            </h2>
          </div>
          <button
            onClick={onCancel}
            className="p-2 text-savage-white/60 hover:text-savage-white transition-colors duration-200"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Object.entries(config.fields).map(([key, field]) => (
              <div key={key} className={field.type === 'textarea' && (field as any).rows && (field as any).rows > 4 ? 'md:col-span-2' : ''}>
                {renderField(key, field)}
              </div>
            ))}
          </div>
        </form>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-4 p-6 border-t border-glass-border">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-savage-white/80 hover:text-savage-white transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={loading}
            className="btn btn-primary flex items-center space-x-2"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-savage-black"></div>
            ) : (
              <Save className="w-4 h-4" />
            )}
            <span>{loading ? 'Saving...' : 'Save'}</span>
          </button>
        </div>
      </motion.div>
    </motion.div>
  )
}
