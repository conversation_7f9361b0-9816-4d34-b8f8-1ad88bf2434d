'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  Eye,
  Calendar,
  Music,
  Package,
  FileText
} from 'lucide-react'
import { ANIMATION_VARIANTS, TRANSITIONS } from '@/lib/constants'

interface ContentItem {
  id: string
  title: string
  type: 'blog' | 'track' | 'product' | 'event'
  status: 'published' | 'draft' | 'archived'
  createdAt: string
  updatedAt: string
  featured?: boolean
}

interface ContentManagerProps {
  contentType: 'blog' | 'track' | 'product' | 'event'
  onEdit: (item: ContentItem) => void
  onDelete: (id: string) => void
  onCreate: () => void
}

const contentTypeConfig = {
  blog: {
    title: 'Blog Posts',
    icon: FileText,
    createLabel: 'New Blog Post',
    fields: ['title', 'excerpt', 'author', 'category', 'published']
  },
  track: {
    title: 'Music Tracks',
    icon: Music,
    createLabel: 'Add New Track',
    fields: ['title', 'artist', 'album', 'duration', 'genre']
  },
  product: {
    title: 'Products',
    icon: Package,
    createLabel: 'Add New Product',
    fields: ['name', 'price', 'category', 'inventory', 'inStock']
  },
  event: {
    title: 'Events',
    icon: Calendar,
    createLabel: 'Create New Event',
    fields: ['title', 'date', 'venue', 'location', 'ticketUrl']
  }
}

export default function ContentManager({ 
  contentType, 
  onEdit, 
  onDelete, 
  onCreate 
}: ContentManagerProps) {
  const [items, setItems] = useState<ContentItem[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'published' | 'draft' | 'archived'>('all')
  const [selectedItems, setSelectedItems] = useState<string[]>([])

  const config = contentTypeConfig[contentType]
  const Icon = config.icon

  useEffect(() => {
    fetchContent()
  }, [contentType]) // eslint-disable-line react-hooks/exhaustive-deps

  const fetchContent = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/content?type=${contentType}`)
      const data = await response.json()
      
      if (response.ok) {
        setItems(data.data || [])
      } else {
        console.error('Failed to fetch content:', data.error)
      }
    } catch (error) {
      console.error('Error fetching content:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this item?')) return

    try {
      const response = await fetch(`/api/admin/content?type=${contentType}&id=${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setItems(items.filter(item => item.id !== id))
        onDelete(id)
      } else {
        console.error('Failed to delete item')
      }
    } catch (error) {
      console.error('Error deleting item:', error)
    }
  }

  const handleBulkDelete = async () => {
    if (!confirm(`Are you sure you want to delete ${selectedItems.length} items?`)) return

    try {
      await Promise.all(
        selectedItems.map(id => 
          fetch(`/api/admin/content?type=${contentType}&id=${id}`, {
            method: 'DELETE'
          })
        )
      )
      
      setItems(items.filter(item => !selectedItems.includes(item.id)))
      setSelectedItems([])
    } catch (error) {
      console.error('Error bulk deleting items:', error)
    }
  }

  const filteredItems = items.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const toggleSelectItem = (id: string) => {
    setSelectedItems(prev => 
      prev.includes(id) 
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    )
  }

  const toggleSelectAll = () => {
    setSelectedItems(
      selectedItems.length === filteredItems.length 
        ? [] 
        : filteredItems.map(item => item.id)
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-savage-yellow"></div>
      </div>
    )
  }

  return (
    <motion.div
      className="space-y-6"
      variants={ANIMATION_VARIANTS.slideUp}
      initial="hidden"
      animate="visible"
      transition={TRANSITIONS.default}
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center space-x-3">
          <Icon className="w-6 h-6 text-savage-yellow" />
          <h2 className="text-2xl font-semibold text-savage-white">
            {config.title}
          </h2>
        </div>
        
        <button
          onClick={onCreate}
          className="btn btn-primary flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>{config.createLabel}</span>
        </button>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-savage-white/60" />
          <input
            type="text"
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 glass rounded-lg text-savage-white placeholder-savage-white/60 focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
          />
        </div>
        
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value as any)}
          className="px-4 py-2 glass rounded-lg text-savage-white focus:outline-none focus:ring-2 focus:ring-savage-yellow border-0"
        >
          <option value="all">All Status</option>
          <option value="published">Published</option>
          <option value="draft">Draft</option>
          <option value="archived">Archived</option>
        </select>
      </div>

      {/* Bulk Actions */}
      {selectedItems.length > 0 && (
        <div className="flex items-center justify-between p-4 glass rounded-lg">
          <span className="text-savage-white">
            {selectedItems.length} item(s) selected
          </span>
          <button
            onClick={handleBulkDelete}
            className="btn btn-danger flex items-center space-x-2"
          >
            <Trash2 className="w-4 h-4" />
            <span>Delete Selected</span>
          </button>
        </div>
      )}

      {/* Content Table */}
      <div className="glass rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-glass-light">
              <tr>
                <th className="px-4 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedItems.length === filteredItems.length && filteredItems.length > 0}
                    onChange={toggleSelectAll}
                    className="rounded border-savage-white/20 bg-transparent text-savage-yellow focus:ring-savage-yellow"
                  />
                </th>
                <th className="px-4 py-3 text-left text-savage-white font-medium">Title</th>
                <th className="px-4 py-3 text-left text-savage-white font-medium">Status</th>
                <th className="px-4 py-3 text-left text-savage-white font-medium">Created</th>
                <th className="px-4 py-3 text-left text-savage-white font-medium">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-glass-border">
              {filteredItems.map((item, index) => (
                <motion.tr
                  key={item.id}
                  className="hover:bg-glass-light transition-colors duration-200"
                  variants={ANIMATION_VARIANTS.slideUp}
                  initial="hidden"
                  animate="visible"
                  transition={{ ...TRANSITIONS.default, delay: index * 0.05 }}
                >
                  <td className="px-4 py-3">
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(item.id)}
                      onChange={() => toggleSelectItem(item.id)}
                      className="rounded border-savage-white/20 bg-transparent text-savage-yellow focus:ring-savage-yellow"
                    />
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center space-x-3">
                      <div>
                        <p className="text-savage-white font-medium">{item.title}</p>
                        {item.featured && (
                          <span className="inline-block px-2 py-1 text-xs bg-savage-yellow text-savage-black rounded-full mt-1">
                            Featured
                          </span>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                      item.status === 'published' 
                        ? 'bg-green-500/20 text-green-400' 
                        : item.status === 'draft'
                        ? 'bg-yellow-500/20 text-yellow-400'
                        : 'bg-gray-500/20 text-gray-400'
                    }`}>
                      {item.status}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-savage-white/80">
                    {new Date(item.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => onEdit(item)}
                        className="p-2 text-savage-white/60 hover:text-savage-yellow transition-colors duration-200"
                        title="Edit"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(item.id)}
                        className="p-2 text-savage-white/60 hover:text-red-400 transition-colors duration-200"
                        title="Delete"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <Icon className="w-12 h-12 text-savage-white/40 mx-auto mb-4" />
            <p className="text-savage-white/60">
              {searchQuery || statusFilter !== 'all' 
                ? 'No items match your filters' 
                : `No ${config.title.toLowerCase()} found`
              }
            </p>
          </div>
        )}
      </div>
    </motion.div>
  )
}
