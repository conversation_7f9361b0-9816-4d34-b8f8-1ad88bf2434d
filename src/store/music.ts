import { create } from 'zustand'
import type { Track, Album } from '@/types'

interface MusicStore {
  // Current playback state
  currentTrack: Track | null
  isPlaying: boolean
  volume: number
  currentTime: number
  duration: number
  
  // Playlist state
  playlist: Track[]
  currentIndex: number
  shuffle: boolean
  repeat: 'none' | 'one' | 'all'
  
  // UI state
  isPlayerVisible: boolean
  
  // Actions
  playTrack: (track: Track, playlist?: Track[]) => void
  pauseTrack: () => void
  resumeTrack: () => void
  stopTrack: () => void
  nextTrack: () => void
  previousTrack: () => void
  seekTo: (time: number) => void
  setVolume: (volume: number) => void
  toggleShuffle: () => void
  toggleRepeat: () => void
  setPlaylist: (tracks: Track[]) => void
  togglePlayer: () => void
  updateProgress: (currentTime: number, duration: number) => void
}

export const useMusicStore = create<MusicStore>((set, get) => ({
  // Initial state
  currentTrack: null,
  isPlaying: false,
  volume: 0.8,
  currentTime: 0,
  duration: 0,
  playlist: [],
  currentIndex: -1,
  shuffle: false,
  repeat: 'none',
  isPlayerVisible: false,

  // Actions
  playTrack: (track, playlist) => {
    const { playlist: currentPlaylist } = get()
    const newPlaylist = playlist || currentPlaylist
    const trackIndex = newPlaylist.findIndex(t => t.id === track.id)
    
    set({
      currentTrack: track,
      isPlaying: true,
      playlist: newPlaylist,
      currentIndex: trackIndex >= 0 ? trackIndex : 0,
      isPlayerVisible: true,
    })
  },

  pauseTrack: () => {
    set({ isPlaying: false })
  },

  resumeTrack: () => {
    set({ isPlaying: true })
  },

  stopTrack: () => {
    set({
      currentTrack: null,
      isPlaying: false,
      currentTime: 0,
      duration: 0,
    })
  },

  nextTrack: () => {
    const { playlist, currentIndex, shuffle, repeat } = get()
    
    if (playlist.length === 0) return
    
    let nextIndex: number
    
    if (shuffle) {
      nextIndex = Math.floor(Math.random() * playlist.length)
    } else {
      nextIndex = currentIndex + 1
      
      if (nextIndex >= playlist.length) {
        if (repeat === 'all') {
          nextIndex = 0
        } else {
          return // End of playlist
        }
      }
    }
    
    const nextTrack = playlist[nextIndex]
    if (nextTrack) {
      set({
        currentTrack: nextTrack,
        currentIndex: nextIndex,
        isPlaying: true,
        currentTime: 0,
      })
    }
  },

  previousTrack: () => {
    const { playlist, currentIndex, currentTime } = get()
    
    if (playlist.length === 0) return
    
    // If more than 3 seconds into the track, restart current track
    if (currentTime > 3) {
      set({ currentTime: 0 })
      return
    }
    
    let prevIndex = currentIndex - 1
    
    if (prevIndex < 0) {
      prevIndex = playlist.length - 1
    }
    
    const prevTrack = playlist[prevIndex]
    if (prevTrack) {
      set({
        currentTrack: prevTrack,
        currentIndex: prevIndex,
        isPlaying: true,
        currentTime: 0,
      })
    }
  },

  seekTo: (time) => {
    set({ currentTime: time })
  },

  setVolume: (volume) => {
    set({ volume: Math.max(0, Math.min(1, volume)) })
  },

  toggleShuffle: () => {
    set(state => ({ shuffle: !state.shuffle }))
  },

  toggleRepeat: () => {
    set(state => ({
      repeat: state.repeat === 'none' 
        ? 'all' 
        : state.repeat === 'all' 
        ? 'one' 
        : 'none'
    }))
  },

  setPlaylist: (tracks) => {
    set({ playlist: tracks })
  },

  togglePlayer: () => {
    set(state => ({ isPlayerVisible: !state.isPlayerVisible }))
  },

  updateProgress: (currentTime, duration) => {
    set({ currentTime, duration })
    
    // Auto-advance to next track when current track ends
    const { repeat, nextTrack } = get()
    if (currentTime >= duration && duration > 0) {
      if (repeat === 'one') {
        set({ currentTime: 0 })
      } else {
        nextTrack()
      }
    }
  },
}))
