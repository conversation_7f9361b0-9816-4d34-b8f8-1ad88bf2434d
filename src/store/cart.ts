import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { Cart, CartItem, Product } from '@/types'

interface CartStore extends Cart {
  // Actions
  addItem: (product: Product, quantity?: number, variant?: { color?: string; size?: string }) => void
  removeItem: (itemId: string) => void
  updateQuantity: (itemId: string, quantity: number) => void
  clearCart: () => void
  toggleCart: () => void
  
  // UI State
  isOpen: boolean
  
  // Computed values
  getItemCount: () => number
  getSubtotal: () => number
  getTotal: () => number
  calculateTotals: () => void
}

const SHIPPING_RATE = 10.00
const TAX_RATE = 0.08

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      // Initial state
      items: [],
      total: 0,
      subtotal: 0,
      shipping: 0,
      tax: 0,
      itemCount: 0,
      isOpen: false,

      // Actions
      addItem: (product, quantity = 1, variant) => {
        const { items } = get()
        const itemId = `${product.id}-${variant?.color || ''}-${variant?.size || ''}`
        
        const existingItem = items.find(item => item.id === itemId)
        
        if (existingItem) {
          get().updateQuantity(itemId, existingItem.quantity + quantity)
        } else {
          const newItem: CartItem = {
            id: itemId,
            productId: product.id,
            name: product.name,
            price: product.price,
            quantity,
            image: product.images[0],
            color: variant?.color,
            size: variant?.size,
          }
          
          set(state => ({
            items: [...state.items, newItem],
          }))
        }
        
        get().calculateTotals()
      },

      removeItem: (itemId) => {
        set(state => ({
          items: state.items.filter(item => item.id !== itemId),
        }))
        get().calculateTotals()
      },

      updateQuantity: (itemId, quantity) => {
        if (quantity <= 0) {
          get().removeItem(itemId)
          return
        }
        
        set(state => ({
          items: state.items.map(item =>
            item.id === itemId ? { ...item, quantity } : item
          ),
        }))
        get().calculateTotals()
      },

      clearCart: () => {
        set({
          items: [],
          total: 0,
          subtotal: 0,
          shipping: 0,
          tax: 0,
          itemCount: 0,
        })
      },

      toggleCart: () => {
        set(state => ({ isOpen: !state.isOpen }))
      },

      // Computed values
      getItemCount: () => {
        const { items } = get()
        return items.reduce((count, item) => count + item.quantity, 0)
      },

      getSubtotal: () => {
        const { items } = get()
        return items.reduce((total, item) => total + (item.price * item.quantity), 0)
      },

      getTotal: () => {
        const subtotal = get().getSubtotal()
        const shipping = subtotal > 0 ? SHIPPING_RATE : 0
        const tax = subtotal * TAX_RATE
        return subtotal + shipping + tax
      },

      // Internal method to calculate totals
      calculateTotals: () => {
        const subtotal = get().getSubtotal()
        const itemCount = get().getItemCount()
        const shipping = subtotal > 0 ? SHIPPING_RATE : 0
        const tax = subtotal * TAX_RATE
        const total = subtotal + shipping + tax

        set({
          subtotal,
          itemCount,
          shipping,
          tax,
          total,
        })
      },
    } as CartStore),
    {
      name: 'savage-cart',
      partialize: (state) => ({
        items: state.items,
        total: state.total,
        subtotal: state.subtotal,
        shipping: state.shipping,
        tax: state.tax,
        itemCount: state.itemCount,
      }),
    }
  )
)
