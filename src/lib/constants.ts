// Navigation items
export const NAVIGATION_ITEMS = [
  { label: 'Home', href: '/' },
  { label: 'Music', href: '/music' },
  { label: 'Shop', href: '/shop' },
  { label: 'Blog', href: '/blog' },
  { label: 'About', href: '/about' },
  { label: 'Contact', href: '/contact' },
] as const

// Product categories
export const PRODUCT_CATEGORIES = [
  { value: 'all', label: 'All Items' },
  { value: 'clothing', label: 'Clothing' },
  { value: 'music', label: 'Music' },
  { value: 'accessories', label: 'Accessories' },
  { value: 'bundles', label: 'Bundles' },
] as const

// Color options
export const PRODUCT_COLORS = [
  { name: 'Black', value: 'black', hex: '#000000' },
  { name: 'White', value: 'white', hex: '#FFFFFF' },
  { name: 'Red', value: 'red', hex: '#FF3E41' },
  { name: 'Yellow', value: 'yellow', hex: '#FFD700' },
] as const

// Size options
export const PRODUCT_SIZES = [
  { name: 'XS', value: 'xs' },
  { name: 'S', value: 's' },
  { name: 'M', value: 'm' },
  { name: 'L', value: 'l' },
  { name: 'XL', value: 'xl' },
  { name: 'XXL', value: 'xxl' },
] as const

// Streaming platforms
export const STREAMING_PLATFORMS = [
  {
    name: 'Spotify',
    value: 'spotify',
    icon: 'spotify',
    color: '#1DB954',
  },
  {
    name: 'Apple Music',
    value: 'apple',
    icon: 'apple',
    color: '#FA243C',
  },
  {
    name: 'SoundCloud',
    value: 'soundcloud',
    icon: 'soundcloud',
    color: '#FF5500',
  },
  {
    name: 'YouTube Music',
    value: 'youtube',
    icon: 'youtube',
    color: '#FF0000',
  },
  {
    name: 'Bandcamp',
    value: 'bandcamp',
    icon: 'bandcamp',
    color: '#629AA0',
  },
] as const

// Social media platforms
export const SOCIAL_PLATFORMS = [
  {
    name: 'Instagram',
    value: 'instagram',
    icon: 'instagram',
    url: 'https://instagram.com/ace_monet_music',
  },
  {
    name: 'Twitter',
    value: 'twitter',
    icon: 'twitter',
    url: 'https://twitter.com/ace_monet',
  },
  {
    name: 'YouTube',
    value: 'youtube',
    icon: 'youtube',
    url: 'https://youtube.com/ace_monet_music',
  },
  {
    name: 'TikTok',
    value: 'tiktok',
    icon: 'tiktok',
    url: 'https://tiktok.com/@ace_monet',
  },
  {
    name: 'Spotify',
    value: 'spotify',
    icon: 'spotify',
    url: 'https://open.spotify.com/artist/ace_monet',
  },
] as const

// Animation variants for Framer Motion
export const ANIMATION_VARIANTS = {
  fadeIn: {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
  },
  slideUp: {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0 },
  },
  slideDown: {
    hidden: { opacity: 0, y: -50 },
    visible: { opacity: 1, y: 0 },
  },
  slideLeft: {
    hidden: { opacity: 0, x: 50 },
    visible: { opacity: 1, x: 0 },
  },
  slideRight: {
    hidden: { opacity: 0, x: -50 },
    visible: { opacity: 1, x: 0 },
  },
  scale: {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { opacity: 1, scale: 1 },
  },
  stagger: {
    visible: {
      transition: {
        staggerChildren: 0.1,
      },
    },
  },
} as const

// Transition presets
export const TRANSITIONS = {
  default: { duration: 0.3, ease: 'easeOut' },
  slow: { duration: 0.6, ease: [0.16, 1, 0.3, 1] },
  fast: { duration: 0.15, ease: 'easeOut' },
  spring: { type: 'spring', stiffness: 300, damping: 30 },
  bounce: { type: 'spring', stiffness: 400, damping: 10 },
} as const

// Breakpoints (matching Tailwind)
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const

// API endpoints
export const API_ENDPOINTS = {
  products: '/api/products',
  cart: '/api/cart',
  checkout: '/api/checkout',
  newsletter: '/api/newsletter',
  contact: '/api/contact',
  music: '/api/music',
  shows: '/api/shows',
  blog: '/api/blog',
} as const

// Error messages
export const ERROR_MESSAGES = {
  generic: 'Something went wrong. Please try again.',
  network: 'Network error. Please check your connection.',
  validation: 'Please check your input and try again.',
  notFound: 'The requested resource was not found.',
  unauthorized: 'You are not authorized to perform this action.',
  serverError: 'Server error. Please try again later.',
} as const

// Success messages
export const SUCCESS_MESSAGES = {
  addedToCart: 'Item added to cart successfully!',
  removedFromCart: 'Item removed from cart.',
  subscribed: 'Successfully subscribed to newsletter!',
  messageSent: 'Message sent successfully!',
  orderPlaced: 'Order placed successfully!',
} as const

// Local storage keys
export const STORAGE_KEYS = {
  cart: 'savage-cart',
  theme: 'savage-theme',
  preferences: 'savage-preferences',
} as const

// Image sizes for optimization
export const IMAGE_SIZES = {
  thumbnail: { width: 150, height: 150 },
  small: { width: 300, height: 300 },
  medium: { width: 600, height: 600 },
  large: { width: 1200, height: 1200 },
  hero: { width: 1920, height: 1080 },
} as const
