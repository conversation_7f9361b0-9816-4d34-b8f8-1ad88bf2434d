// Product types
export interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  images: string[]
  category: ProductCategory
  colors?: Color[]
  sizes?: Size[]
  stock: number
  badge?: ProductBadge
  featured?: boolean
  slug: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
}

export interface ProductVariant {
  id: string
  productId: string
  color?: string
  size?: string
  price: number
  stock: number
  sku: string
}

export type ProductCategory = 'clothing' | 'music' | 'accessories' | 'bundles' | 'all'

export type ProductBadge = 'new' | 'bestseller' | 'limited' | 'sale'

export interface Color {
  name: string
  value: string
  hex: string
}

export interface Size {
  name: string
  value: string
  inStock: boolean
}

// Cart types
export interface CartItem {
  id: string
  productId: string
  name: string
  price: number
  quantity: number
  image: string
  color?: string
  size?: string
  variant?: ProductVariant
}

export interface Cart {
  items: CartItem[]
  total: number
  subtotal: number
  shipping: number
  tax: number
  itemCount: number
}

// Music types
export interface Track {
  id: string
  title: string
  artist: string
  album: string
  duration: number
  url: string
  artwork: string
  releaseDate: Date
  genre: string[]
  featured?: boolean
}

export interface Album {
  id: string
  title: string
  artist: string
  artwork: string
  releaseDate: Date
  tracks: Track[]
  genre: string[]
  description: string
  streamingLinks: StreamingLink[]
}

export interface StreamingLink {
  platform: StreamingPlatform
  url: string
  icon: string
}

export type StreamingPlatform = 'spotify' | 'apple' | 'soundcloud' | 'youtube' | 'bandcamp'

// Show/Event types
export interface Show {
  id: string
  title: string
  venue: string
  city: string
  state: string
  country: string
  date: Date
  time: string
  ticketUrl: string
  price?: number
  soldOut?: boolean
  featured?: boolean
}

// Blog types
export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  author: string
  publishedAt: Date
  updatedAt: Date
  featuredImage: string
  tags: string[]
  category: string
  featured?: boolean
}

// User types
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  avatar?: string
  createdAt: Date
  preferences: UserPreferences
}

export interface UserPreferences {
  newsletter: boolean
  notifications: boolean
  theme: 'dark' | 'light'
}

// Newsletter types
export interface NewsletterSubscription {
  email: string
  subscribedAt: Date
  active: boolean
}

// Contact types
export interface ContactMessage {
  id: string
  name: string
  email: string
  subject: string
  message: string
  createdAt: Date
  status: 'new' | 'read' | 'replied'
}

// API Response types
export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Form types
export interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
}

export interface NewsletterFormData {
  email: string
}

export interface CheckoutFormData {
  email: string
  firstName: string
  lastName: string
  address: string
  city: string
  state: string
  zipCode: string
  country: string
  phone?: string
}

// Navigation types
export interface NavItem {
  label: string
  href: string
  active?: boolean
  children?: NavItem[]
}

// SEO types
export interface SEOData {
  title: string
  description: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product'
}
