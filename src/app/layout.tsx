import type { Metadata } from 'next'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>_Neue } from 'next/font/google'
import GoogleAnalytics from '@/components/analytics/GoogleAnalytics'
import JsonLd, { schemas } from '@/components/seo/JsonLd'
import './globals.css'

const montserrat = Montserrat({
  subsets: ['latin'],
  variable: '--font-montserrat',
  display: 'swap',
})

const bebasNeue = Bebas_Neue({
  subsets: ['latin'],
  weight: '400',
  variable: '--font-bebas-neue',
  display: 'swap',
})

export const metadata: Metadata = {
  title: {
    default: 'Worship Leader | Christian Music & Ministry Store',
    template: '%s | Worship Leader'
  },
  description: 'Lifting Hearts to Heaven, One Song at a Time. Official Christian music, merchandise, and ministry resources.',
  keywords: ['christian music', 'worship', 'ministry', 'merchandise', 'ecommerce', 'praise', 'faith'],
  authors: [{ name: 'Worship Leader' }],
  creator: 'Worship Leader',
  publisher: 'Worship Leader',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://worship-leader.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://worship-leader.com',
    title: 'Worship Leader | Christian Music & Ministry Store',
    description: 'Lifting Hearts to Heaven, One Song at a Time. Official Christian music, merchandise, and ministry resources.',
    siteName: 'Worship Leader',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Worship Leader - Christian Music Artist',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Worship Leader | Christian Music & Ministry Store',
    description: 'Lifting Hearts to Heaven, One Song at a Time. Official Christian music, merchandise, and ministry resources.',
    images: ['/og-image.jpg'],
    creator: '@worship_leader',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${montserrat.variable} ${bebasNeue.variable}`}>
      <head>
        <JsonLd data={schemas.organization} />
        <JsonLd data={schemas.musicGroup} />
      </head>
      <body className="antialiased">
        <GoogleAnalytics measurementId={process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || ''} />
        {children}
      </body>
    </html>
  )
}
