import type { Metada<PERSON> } from 'next'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>_Neue } from 'next/font/google'
import GoogleAnalytics from '@/components/analytics/GoogleAnalytics'
import JsonLd, { schemas } from '@/components/seo/JsonLd'
import './globals.css'

const montserrat = Montserrat({
  subsets: ['latin'],
  variable: '--font-montserrat',
  display: 'swap',
})

const bebasNeue = Bebas_Neue({
  subsets: ['latin'],
  weight: '400',
  variable: '--font-bebas-neue',
  display: 'swap',
})

export const metadata: Metadata = {
  title: {
    default: 'Ace Monet | Inspiring Music & Artist Store',
    template: '%s | Ace Monet'
  },
  description: 'Inspiring Hearts Through Music, One Song at a Time. Official music, merchandise, and exclusive content from artist Ace Monet.',
  keywords: ['music artist', 'inspiration', 'original music', 'merchandise', 'ecommerce', 'creativity', 'art'],
  authors: [{ name: 'Ace Monet' }],
  creator: '<PERSON> Monet',
  publisher: 'Ace Monet',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://ace-monet.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://ace-monet.com',
    title: 'Ace Monet | Inspiring Music & Artist Store',
    description: 'Inspiring Hearts Through Music, One Song at a Time. Official music, merchandise, and exclusive content from artist Ace Monet.',
    siteName: 'Ace Monet',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Ace Monet - Inspiring Music Artist',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Ace Monet | Inspiring Music & Artist Store',
    description: 'Inspiring Hearts Through Music, One Song at a Time. Official music, merchandise, and exclusive content from artist Ace Monet.',
    images: ['/og-image.jpg'],
    creator: '@ace_monet',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${montserrat.variable} ${bebasNeue.variable}`}>
      <head>
        <JsonLd data={schemas.organization} />
        <JsonLd data={schemas.musicGroup} />
      </head>
      <body className="antialiased">
        <GoogleAnalytics measurementId={process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || ''} />
        {children}
      </body>
    </html>
  )
}
