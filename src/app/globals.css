@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Montserrat:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }
  
  body {
    @apply bg-savage-black text-savage-white font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-savage-dark;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gradient-primary rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gradient-primary-hover;
  }
}

@layer components {
  /* Glass morphism effect */
  .glass {
    @apply bg-glass-bg backdrop-blur-md border border-glass-border;
  }

  .glass-light {
    @apply bg-glass-light backdrop-blur-sm border border-glass-border;
  }

  /* Button styles */
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-savage-black;
  }

  .btn-primary {
    @apply bg-gradient-primary text-savage-black hover:bg-gradient-primary-hover focus:ring-savage-yellow shadow-glow;
  }

  .btn-secondary {
    @apply glass text-savage-white hover:bg-glass-light focus:ring-savage-red border-savage-yellow/20;
  }

  .btn-outline {
    @apply border-2 border-savage-yellow text-savage-yellow hover:bg-savage-yellow hover:text-savage-black focus:ring-savage-yellow;
  }

  /* Card styles */
  .card {
    @apply glass rounded-xl p-6 shadow-glass;
  }

  .card-hover {
    @apply card hover:scale-105 hover:shadow-glow transition-all duration-300;
  }

  /* Text gradient */
  .text-gradient {
    @apply bg-gradient-text bg-clip-text text-transparent;
  }

  /* Glitch effect */
  .glitch {
    position: relative;
    color: #fff;
    font-size: 4rem;
    font-weight: bold;
    text-transform: uppercase;
    animation: glitch 2s infinite;
  }

  .glitch::before,
  .glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .glitch::before {
    animation: glitch 2s infinite;
    color: #ff3e41;
    z-index: -1;
  }

  .glitch::after {
    animation: glitch 2s infinite;
    color: #ffd700;
    z-index: -2;
  }

  /* Section padding */
  .section-padding {
    @apply py-16 md:py-24;
  }

  /* Container */
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Responsive grid */
  .grid-responsive {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  .grid-responsive-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Animation delays */
  .delay-100 {
    animation-delay: 100ms;
  }

  .delay-200 {
    animation-delay: 200ms;
  }

  .delay-300 {
    animation-delay: 300ms;
  }

  .delay-400 {
    animation-delay: 400ms;
  }

  .delay-500 {
    animation-delay: 500ms;
  }
}
