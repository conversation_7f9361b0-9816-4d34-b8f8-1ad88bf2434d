import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { sendEmail, emailTemplates } from '../../../../lib/email'

export async function POST(req: NextRequest) {
  try {
    const { name, email, subject, message } = await req.json()

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      )
    }

    // Save to database
    const submission = await prisma.contactSubmission.create({
      data: {
        name,
        email,
        subject,
        message,
      },
    })

    // Send email notification to admin
    const adminEmail = process.env.ADMIN_EMAIL
    if (adminEmail) {
      const emailTemplate = emailTemplates.contactFormSubmission(name, email, subject, message)
      await sendEmail({
        to: adminEmail,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
      })
    }

    // Send confirmation email to user
    await sendEmail({
      to: email,
      subject: 'Thank you for contacting us',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #1a1a1a;">Thank You for Reaching Out!</h1>
          <p>Dear ${name},</p>
          <p>We've received your message and will get back to you within 24-48 hours.</p>
          <p>Your message:</p>
          <div style="background: #f5f5f5; padding: 15px; border-radius: 6px; margin: 15px 0;">
            <p><strong>Subject:</strong> ${subject}</p>
            <p style="white-space: pre-wrap;">${message}</p>
          </div>
          <p>Blessings,<br>The Worship Leader Team</p>
        </div>
      `,
    })

    return NextResponse.json({
      success: true,
      message: 'Message sent successfully',
      id: submission.id,
    })
  } catch (error) {
    console.error('Contact form error:', error)
    return NextResponse.json(
      { error: 'Failed to send message' },
      { status: 500 }
    )
  }
}
