import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET - Fetch all content
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user || (session.user as any).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const type = searchParams.get('type')

    switch (type) {
      case 'blog':
        const blogPosts = await prisma.blogPost.findMany({
          orderBy: { createdAt: 'desc' }
        })
        return NextResponse.json({ data: blogPosts })

      case 'tracks':
        const tracks = await prisma.track.findMany({
          orderBy: { createdAt: 'desc' }
        })
        return NextResponse.json({ data: tracks })

      case 'products':
        const products = await prisma.product.findMany({
          orderBy: { createdAt: 'desc' }
        })
        return NextResponse.json({ data: products })

      case 'events':
        const events = await prisma.show.findMany({
          orderBy: { date: 'desc' }
        })
        return NextResponse.json({ data: events })

      default:
        return NextResponse.json({ error: 'Invalid content type' }, { status: 400 })
    }
  } catch (error) {
    console.error('Content fetch error:', error)
    return NextResponse.json({ error: 'Failed to fetch content' }, { status: 500 })
  }
}

// POST - Create new content
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user || (session.user as any).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { type, data } = await req.json()

    switch (type) {
      case 'blog':
        const newBlogPost = await prisma.blogPost.create({
          data: {
            title: data.title,
            slug: data.slug,
            excerpt: data.excerpt,
            content: data.content,
            author: data.author,
            featuredImage: data.featuredImage,
            tags: data.tags,
            category: data.category,
            featured: data.featured || false,
            published: data.published || false,
            publishedAt: data.published ? new Date() : null,
          }
        })
        return NextResponse.json({ data: newBlogPost })

      case 'track':
        const newTrack = await prisma.track.create({
          data: {
            title: data.title,
            artist: data.artist,
            album: data.album,
            duration: data.duration,
            artwork: data.artwork,
            genre: data.genre,
            featured: data.featured || false,
            releaseDate: new Date(data.releaseDate),
          }
        })
        return NextResponse.json({ data: newTrack })

      case 'product':
        const newProduct = await prisma.product.create({
          data: {
            name: data.name,
            slug: data.slug || data.name.toLowerCase().replace(/\s+/g, '-'),
            description: data.description,
            price: data.price,
            category: data.category,
            images: data.images || [],
            tags: data.tags || [],
            featured: data.featured || false,
            stock: data.stock || 0,
          }
        })
        return NextResponse.json({ data: newProduct })

      case 'event':
        const eventDate = new Date(data.date)
        const newEvent = await prisma.show.create({
          data: {
            title: data.title,
            venue: data.venue,
            city: data.city,
            state: data.state || '',
            country: data.country || 'US',
            date: eventDate,
            time: eventDate.toLocaleTimeString('en-US', { hour12: false }),
            ticketUrl: data.ticketUrl,
            featured: data.featured || false,
          }
        })
        return NextResponse.json({ data: newEvent })

      default:
        return NextResponse.json({ error: 'Invalid content type' }, { status: 400 })
    }
  } catch (error) {
    console.error('Content creation error:', error)
    return NextResponse.json({ error: 'Failed to create content' }, { status: 500 })
  }
}

// PUT - Update content
export async function PUT(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user || (session.user as any).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { type, id, data } = await req.json()

    switch (type) {
      case 'blog':
        const updatedBlogPost = await prisma.blogPost.update({
          where: { id },
          data: {
            ...data,
            updatedAt: new Date(),
            publishedAt: data.published && !data.publishedAt ? new Date() : data.publishedAt,
          }
        })
        return NextResponse.json({ data: updatedBlogPost })

      case 'track':
        const updatedTrack = await prisma.track.update({
          where: { id },
          data: {
            ...data,
            updatedAt: new Date(),
          }
        })
        return NextResponse.json({ data: updatedTrack })

      case 'product':
        const updatedProduct = await prisma.product.update({
          where: { id },
          data: {
            ...data,
            updatedAt: new Date(),
          }
        })
        return NextResponse.json({ data: updatedProduct })

      case 'event':
        const updatedEvent = await prisma.show.update({
          where: { id },
          data: {
            ...data,
            updatedAt: new Date(),
          }
        })
        return NextResponse.json({ data: updatedEvent })

      default:
        return NextResponse.json({ error: 'Invalid content type' }, { status: 400 })
    }
  } catch (error) {
    console.error('Content update error:', error)
    return NextResponse.json({ error: 'Failed to update content' }, { status: 500 })
  }
}

// DELETE - Delete content
export async function DELETE(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user || (session.user as any).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const type = searchParams.get('type')
    const id = searchParams.get('id')

    if (!type || !id) {
      return NextResponse.json({ error: 'Missing type or id' }, { status: 400 })
    }

    switch (type) {
      case 'blog':
        await prisma.blogPost.delete({ where: { id } })
        break
      case 'track':
        await prisma.track.delete({ where: { id } })
        break
      case 'product':
        await prisma.product.delete({ where: { id } })
        break
      case 'event':
        await prisma.show.delete({ where: { id } })
        break
      default:
        return NextResponse.json({ error: 'Invalid content type' }, { status: 400 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Content deletion error:', error)
    return NextResponse.json({ error: 'Failed to delete content' }, { status: 500 })
  }
}
