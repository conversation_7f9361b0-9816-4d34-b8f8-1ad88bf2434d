import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'

export async function POST(req: NextRequest) {
  try {
    const { items, customerEmail, shippingAddress, billingAddress } = await req.json()

    // Calculate totals
    const subtotal = items.reduce((total: number, item: any) => {
      return total + (item.price * item.quantity)
    }, 0)
    
    const shipping = subtotal > 0 ? 10.00 : 0 // $10 shipping
    const tax = subtotal * 0.08 // 8% tax
    const total = subtotal + shipping + tax

    // Create order in database
    const order = await prisma.order.create({
      data: {
        orderNumber: `WL-${Date.now()}`,
        status: 'PENDING',
        total,
        subtotal,
        shipping,
        tax,
        customerEmail,
        shippingAddress,
        billingAddress,
        orderItems: {
          create: items.map((item: any) => ({
            productId: item.id,
            quantity: item.quantity,
            price: item.price,
            options: item.options || {},
          })),
        },
      },
    })

    // Create Stripe payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(total * 100), // Convert to cents
      currency: 'usd',
      metadata: {
        orderId: order.id,
        orderNumber: order.orderNumber,
      },
      receipt_email: customerEmail,
    })

    // Update order with payment intent ID
    await prisma.order.update({
      where: { id: order.id },
      data: { paymentIntentId: paymentIntent.id },
    })

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      orderId: order.id,
      orderNumber: order.orderNumber,
    })
  } catch (error) {
    console.error('Checkout error:', error)
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    )
  }
}
