import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripe } from '../../../../../lib/stripe'
import { prisma } from '@/lib/prisma'

export async function POST(req: NextRequest) {
  const body = await req.text()
  const signature = headers().get('stripe-signature')!

  let event

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )
  } catch (err) {
    console.error('Webhook signature verification failed:', err)
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
  }

  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object

        // Find and update order status
        const successOrder = await prisma.order.findFirst({
          where: { paymentIntentId: paymentIntent.id },
        })

        if (successOrder) {
          await prisma.order.update({
            where: { id: successOrder.id },
            data: { status: 'PROCESSING' },
          })
        }

        console.log('Payment succeeded:', paymentIntent.id)
        break

      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object

        // Find and update order status
        const failedOrder = await prisma.order.findFirst({
          where: { paymentIntentId: failedPayment.id },
        })

        if (failedOrder) {
          await prisma.order.update({
            where: { id: failedOrder.id },
            data: { status: 'CANCELLED' },
          })
        }

        console.log('Payment failed:', failedPayment.id)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Webhook handler error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}
