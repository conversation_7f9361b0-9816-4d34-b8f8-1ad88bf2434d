import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Music-themed color palette
        savage: {
          black: '#0a0a0a',
          white: '#ffffff',
          yellow: '#ffd700',
          'yellow-light': '#ffed4a',
          red: '#ff3e41',
          'red-dark': '#d32f2f',
          dark: '#121212',
          darker: '#080808',
          gray: '#1e1e1e',
          'light-gray': '#333333',
        },
        // Glass morphism colors
        glass: {
          bg: 'rgba(18, 18, 18, 0.7)',
          border: 'rgba(255, 255, 255, 0.1)',
          light: 'rgba(255, 255, 255, 0.05)',
        }
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, #ffd700, #ff3e41)',
        'gradient-primary-hover': 'linear-gradient(135deg, #ffed4a, #d32f2f)',
        'gradient-dark': 'linear-gradient(135deg, #0f0f0f, #1a1a1a)',
        'gradient-overlay': 'linear-gradient(to bottom, rgba(10, 10, 10, 0.3), rgba(10, 10, 10, 0.95))',
        'gradient-text': 'linear-gradient(45deg, #ffd700, #ff3e41)',
        'gradient-glass': 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 62, 65, 0.1))',
      },
      fontFamily: {
        sans: ['Montserrat', 'sans-serif'],
        heading: ['Bebas Neue', 'cursive'],
      },
      animation: {
        'glitch': 'glitch 2s infinite',
        'float': 'float 6s ease-in-out infinite',
        'pulse-glow': 'pulse-glow 2s ease-in-out infinite alternate',
        'slide-up': 'slide-up 0.6s ease-out',
        'fade-in': 'fade-in 0.8s ease-out',
      },
      keyframes: {
        glitch: {
          '0%, 100%': { transform: 'translate(0)' },
          '20%': { transform: 'translate(-2px, 2px)' },
          '40%': { transform: 'translate(-2px, -2px)' },
          '60%': { transform: 'translate(2px, 2px)' },
          '80%': { transform: 'translate(2px, -2px)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        'pulse-glow': {
          '0%': { boxShadow: '0 0 20px rgba(255, 215, 0, 0.5)' },
          '100%': { boxShadow: '0 0 40px rgba(255, 62, 65, 0.8)' },
        },
        'slide-up': {
          '0%': { transform: 'translateY(100px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
      },
      backdropBlur: {
        xs: '2px',
      },
      boxShadow: {
        'glass': '0 8px 32px 0 rgba(0, 0, 0, 0.36)',
        'glow': '0 0 20px rgba(255, 215, 0, 0.3)',
        'glow-red': '0 0 20px rgba(255, 62, 65, 0.3)',
      },
    },
  },
  plugins: [],
}

export default config
